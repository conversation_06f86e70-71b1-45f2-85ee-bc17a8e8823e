#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, List, Tuple, Optional

import torch
from torch.utils.data import DataLoader
from torchvision import transforms

from .dataset import AgeGenderDataset


def create_dataloaders(
    data_path: str,
    batch_size: int = 32,
    num_workers: int = 4,
    target_size: Tuple[int, int] = (224, 224),
    with_persons: bool = False,
    disable_faces: bool = False,
    min_age: int = 0,
    max_age: int = 100,
    avg_age: float = 50.0,
    only_age: bool = False,
    train_transform: Optional[transforms.Compose] = None,
    val_transform: Optional[transforms.Compose] = None
) -> <PERSON><PERSON>[DataLoader, DataLoader]:
    """
    Create training and validation data loaders.
    
    Args:
        data_path: Path to the dataset directory
        batch_size: Batch size for training
        num_workers: Number of workers for data loading
        target_size: Target size for the images (height, width)
        with_persons: Whether to include person crops
        disable_faces: Whether to disable face crops
        min_age: Minimum age in the dataset
        max_age: Maximum age in the dataset
        avg_age: Average age in the dataset
        only_age: Whether to predict only age (no gender)
        train_transform: Optional transforms to apply to training images
        val_transform: Optional transforms to apply to validation images
        
    Returns:
        Tuple of (train_loader, val_loader)
    """
    # Set up default transforms if not provided
    if train_transform is None:
        train_transform = transforms.Compose([
            transforms.Resize(target_size),
            transforms.RandomHorizontalFlip(),
            transforms.RandomRotation(10),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    if val_transform is None:
        val_transform = transforms.Compose([
            transforms.Resize(target_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    # Create datasets
    train_dataset = AgeGenderDataset(
        data_path=data_path,
        split="train",
        transform=train_transform,
        target_size=target_size,
        with_persons=with_persons,
        disable_faces=disable_faces,
        min_age=min_age,
        max_age=max_age,
        avg_age=avg_age,
        only_age=only_age
    )
    
    val_dataset = AgeGenderDataset(
        data_path=data_path,
        split="val",
        transform=val_transform,
        target_size=target_size,
        with_persons=with_persons,
        disable_faces=disable_faces,
        min_age=min_age,
        max_age=max_age,
        avg_age=avg_age,
        only_age=only_age
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    return train_loader, val_loader

def collate_fn(batch: List[Tuple[torch.Tensor, Dict]]) -> Tuple[torch.Tensor, Dict]:
    """
    Custom collate function for batching samples.
    
    Args:
        batch: List of (image, target) tuples
        
    Returns:
        Tuple of (images, targets)
    """
    images = []
    targets = []
    
    for image, target in batch:
        images.append(image)
        targets.append(target)
    
    # Stack images
    images = torch.stack(images)
    
    # Prepare targets
    batch_targets = {
        "ages": [t["ages"] for t in targets],
        "genders": [t["genders"] for t in targets],
        "face_boxes": [t["face_boxes"] for t in targets],
        "person_boxes": [t["person_boxes"] for t in targets]
    }
    
    return images, batch_targets 