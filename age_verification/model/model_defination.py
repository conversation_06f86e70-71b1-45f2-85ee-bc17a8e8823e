import torch.nn as nn
from transformers import AutoModelForSequenceClassification, AutoProcessor
from peft import get_peft_model, LoraConfig


def load_gemma3n_model_for_classification(
    model_name,
    num_labels,
    lora_config_dict
):
    """Load the pre-trained Gemma3nForSequenceClassification model and apply LoRA."""
    model = AutoModelForSequenceClassification.from_pretrained(model_name, num_labels=num_labels)

    # Apply LoRA
    lora_config = LoraConfig(**lora_config_dict)
    model = get_peft_model(model, lora_config)

    # CRITICAL: Ensure Classification head is trainable
    # By default get_peft_model freezes all base model parameters
    # While task_type="SEQ_CLS" in LoraConfig should target the classification head for LORA
    # it's crucial to verify or explicitly unfreeze it if it's not covered by LoRA adapters.
    if hasattr(model, 'score') and isinstance(model.score, nn.Module):
        for param in model.score.parameters():
            param.requires_grad = True
    elif hasattr(model, 'classifier') and isinstance(model.classifier, nn.Module):
        for param in model.classifier.parameters():
            param.requires_grad = True
    
    return model


def load_gemma3n_processor(model_name):
    """Load the pre-trained Gemma3n processor."""
    processor = AutoProcessor.from_pretrained(model_name)
    return processor