"""
Code adapted from timm https://github.com/huggingface/pytorch-image-models

Modifications and additions for mivolo by / Copyright 2023, <PERSON><PERSON>, <PERSON>
"""

import os
from typing import Any, Dict, Optional, Union

import timm
import torch
import torch.nn as nn

# register new models
from mivolo.model.mivolo_model import *  # noqa: F403, F401
from timm.layers import set_layer_config
from timm.models._factory import parse_model_name
from timm.models._helpers import load_state_dict, remap_checkpoint
from timm.models._hub import load_model_config_from_hf
from timm.models._pretrained import PretrainedCfg, split_model_name_tag
from timm.models._registry import is_model, model_entrypoint

from collections import OrderedDict # Import if needed by clean_state_dict or the func itself
# You might need other imports that the original timm function uses, like _has_safetensors, safetensors

# Attempt to import safetensors if you plan to support .safetensors files
try:
    import safetensors.torch
    _has_safetensors = True
except ImportError:
    _has_safetensors = False

# _logger = logging.getLogger(__name__) # Use a local logger

def clean_state_dict(state_dict):
    """
    Clean state_dict keys from common prefixes like 'module.' (from DDP/DP)
    or other model-specific prefixes.
    (This is a simplified version, timm's might be more complex.
     Or better, import it directly from timm if possible and not causing issues:
     from timm.models._helpers import clean_state_dict
     If clean_state_dict itself is not problematic, importing it is fine)
    """
    new_state_dict = OrderedDict()
    for k, v in state_dict.items():
        name = k
        if name.startswith('module.'): # common DataParallel/DistributedDataParallel prefix
            name = name[len('module.'):]
        # Add other prefixes to strip if necessary, e.g., from specific model wrappers
        # if name.startswith('encoder.'): name = name[len('encoder.'):]
        new_state_dict[name] = v
    return new_state_dict

# --- THIS IS YOUR LOCAL COPY OF TIMM'S HELPER, MODIFIED ---
def local_timm_load_state_dict(checkpoint_path, use_ema=True): # Renamed to avoid direct override of timm
    if checkpoint_path and os.path.isfile(checkpoint_path):
        if str(checkpoint_path).endswith(".safetensors"):
            if not _has_safetensors:
                # logging.error("Attempted to load safetensors but `pip install safetensors` is missing.")
                raise ImportError("safetensors library not found.")
            checkpoint = safetensors.torch.load_file(checkpoint_path, device='cpu')
        else:
            # --- KEY MODIFICATION HERE ---
            checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
            # --- END MODIFICATION ---

        state_dict_key = ''
        if isinstance(checkpoint, dict):
            # Try to find the actual state dictionary within the checkpoint structure
            if use_ema and checkpoint.get('state_dict_ema', None) is not None:
                state_dict_key = 'state_dict_ema'
            elif use_ema and checkpoint.get('model_ema', None) is not None: # Some save as 'model_ema'
                state_dict_key = 'model_ema'
            elif 'state_dict' in checkpoint:
                state_dict_key = 'state_dict'
            elif 'model' in checkpoint: # Check for 'model' key which is also common
                state_dict_key = 'model'
            # Add other potential keys if known for specific checkpoints
        
        # If a specific key was found, use it; otherwise, assume the checkpoint itself is the state_dict
        extracted_state_dict = checkpoint[state_dict_key] if state_dict_key else checkpoint
        
        # Ensure that extracted_state_dict is actually a dictionary (or OrderedDict)
        if not isinstance(extracted_state_dict, (dict, OrderedDict)):
            # _logger.error(f"Extracted content for state_dict is not a dictionary. Found type: {type(extracted_state_dict)}. Checkpoint structure might be unexpected.")
            raise ValueError("Could not extract a valid state_dict from checkpoint.")

        # Use timm's clean_state_dict if available and not causing issues, otherwise use your simplified one
        try:
            from timm.models._helpers import clean_state_dict as timm_clean_state_dict
            state_dict = timm_clean_state_dict(extracted_state_dict)
        except ImportError:
            # _logger.warning("timm.models._helpers.clean_state_dict not found, using local basic clean_state_dict.")
            state_dict = clean_state_dict(extracted_state_dict) # Call your local simplified one

        # _logger.info("Loaded {} from checkpoint '{}' using local_timm_load_state_dict".format(
            # state_dict_key if state_dict_key else "assumed state_dict", checkpoint_path))
        return state_dict
    else:
        # _logger.error("No checkpoint found at '{}' in local_timm_load_state_dict".format(checkpoint_path))
        raise FileNotFoundError(f"No checkpoint found at {checkpoint_path}")


def load_checkpoint(
    model, checkpoint_path, use_ema=True, strict=False, remap=False, filter_keys=['head.weight', 'head.bias', 'aux_head.weight', 'aux_head.bias'], state_dict_map=None
):
    if os.path.splitext(checkpoint_path)[-1].lower() in (".npz", ".npy"):
        # numpy checkpoint, try to load via model specific load_pretrained fn
        if hasattr(model, "load_pretrained"):
            timm.models._model_builder.load_pretrained(checkpoint_path)
        else:
            raise NotImplementedError("Model cannot load numpy checkpoint")
        return
    state_dict = local_timm_load_state_dict(checkpoint_path, use_ema)
    if remap:
        state_dict = remap_checkpoint(model, state_dict)
    if filter_keys:
        for sd_key in list(state_dict.keys()):
            for filter_key in filter_keys:
                if filter_key in sd_key:
                    if sd_key in state_dict:
                        del state_dict[sd_key]

    rep = []
    if state_dict_map is not None:
        # 'patch_embed.conv1.' : 'patch_embed.conv.'
        for state_k in list(state_dict.keys()):
            for target_k, target_v in state_dict_map.items():
                if target_v in state_k:
                    target_name = state_k.replace(target_v, target_k)
                    state_dict[target_name] = state_dict[state_k]
                    rep.append(state_k)
        for r in rep:
            if r in state_dict:
                del state_dict[r]

    incompatible_keys = model.load_state_dict(state_dict, strict=strict if filter_keys is None else False)
    return incompatible_keys


def create_model(
    model_name: str,
    pretrained: bool = False,
    pretrained_cfg: Optional[Union[str, Dict[str, Any], PretrainedCfg]] = None,
    pretrained_cfg_overlay: Optional[Dict[str, Any]] = None,
    checkpoint_path: str = "",
    scriptable: Optional[bool] = None,
    exportable: Optional[bool] = None,
    no_jit: Optional[bool] = None,
    filter_keys=None,
    state_dict_map=None,
    **kwargs,
):
    """Create a model
    Lookup model's entrypoint function and pass relevant args to create a new model.
    """
    if not filter_keys:
        filter_keys = ['head.weight', 'head.bias', 'aux_head.weight', 'aux_head.bias']
    # Parameters that aren't supported by all models or are intended to only override model defaults if set
    # should default to None in command line args/cfg. Remove them if they are present and not set so that
    # non-supporting models don't break and default args remain in effect.
    kwargs = {k: v for k, v in kwargs.items() if v is not None}

    model_source, model_name = parse_model_name(model_name)
    if model_source == "hf-hub":
        assert not pretrained_cfg, "pretrained_cfg should not be set when sourcing model from Hugging Face Hub."
        # For model names specified in the form `hf-hub:path/architecture_name@revision`,
        # load model weights + pretrained_cfg from Hugging Face hub.
        pretrained_cfg, model_name = load_model_config_from_hf(model_name)
    else:
        model_name, pretrained_tag = split_model_name_tag(model_name)
        if not pretrained_cfg:
            # a valid pretrained_cfg argument takes priority over tag in model name
            pretrained_cfg = pretrained_tag

    if not is_model(model_name):
        raise RuntimeError("Unknown model (%s)" % model_name)

    create_fn = model_entrypoint(model_name)
    with set_layer_config(scriptable=scriptable, exportable=exportable, no_jit=no_jit):
        model = create_fn(
            pretrained=pretrained,
            pretrained_cfg=pretrained_cfg,
            pretrained_cfg_overlay=pretrained_cfg_overlay,
            **kwargs,
        )

    if checkpoint_path:
        load_checkpoint(model, checkpoint_path, filter_keys=filter_keys, state_dict_map=state_dict_map)
    
    # manually initialize the model head for 4 output classes
    model.head = nn.Linear(model.head.in_features, 4)
    model.aux_head = nn.Linear(model.aux_head.in_features, 4)

    # Randomly initilize the model head
    # nn.init.normal_(model.head.weight, mean=0.0, std=0.01)
    # nn.init.normal_(model.aux_head.weight, mean=0.0, std=0.01)

    return model