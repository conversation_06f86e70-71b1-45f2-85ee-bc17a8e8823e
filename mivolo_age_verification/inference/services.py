import os
import logging
import redis
import redis.exceptions
import torch
import argparse
import numpy as np
from deepface import DeepFace
import torch.backends
import torch.backends.cudnn
from mivolo.predictor import Predictor
import pickle
import uuid
import time

_logger = logging.getLogger("inference_api")

embedding_service = None
face_vector_store = None
mivolo_predictor = None
redis_connection = None


# Embedding Service
class EmbeddingService:
    def __init__(self, model_name, logger):
        self.model_name = model_name
        self.logger = logger
        self._load_model()

    def _load_model(self):
        try:
            _logger.info(f"Loading face embedding model: {self.model_name}...")
            dummy_img = np.zeros((100, 100, 3), dtype=np.uint8)  # For pre-loading
            DeepFace.represent(img_path=dummy_img, model_name=self.model_name, enforce_detection=False)
        except Exception as e:
            _logger.error(f"Error loading embedding model '{self.model_name}': {e}", exc_info=True)
            raise RuntimeError(f"Failed to load embedding model: {self.model_name}") from e

    def get_embedding(self, face_crop_image_np):
        if face_crop_image_np is None or face_crop_image_np.size == 0:
            _logger.warning("Cannot get embedding for an empty image.")
            return None
        try:
            # DeepFace.represent returns a list of dicts, each dict has embedding
            embedding_objs = DeepFace.represent(
                img_path=face_crop_image_np,
                model_name=self.model_name,
                enforce_detection=False  # Detection is done by MiVOLO
            )
            if embedding_objs and isinstance(embedding_objs, list) and 'embedding' in embedding_objs[0]:
                return embedding_objs[0]['embedding']  # list of floats
            else:
                _logger.warning(f"DeepFace did not return expected embedding for model {self.model_name}")
                return None
        except Exception as e:
            _logger.error(f"Error generating face embedding: {e}", exc_info=True)
            return None


# Face vector Store
class RedisFaceVectorStore:
    def __init__(self, redis_connection, ttl_seconds, similarity_threshold, embedding_model_name, logger):
        self.redis = redis_connection
        self.ttl_seconds = ttl_seconds
        self.similarity_threshold = similarity_threshold
        self.prefix = "face_vector:"
        self.embedding_model_name = embedding_model_name

    @staticmethod
    def _cosine_similarity(vec1_list, vec2_list):
        vec1 = np.asarray(vec1_list, dtype=np.float32)
        vec2 = np.asarray(vec2_list, dtype=np.float32)
        norm_vec1 = np.linalg.norm(vec1)
        norm_vec2 = np.linalg.norm(vec2)
        if norm_vec1 == 0 or norm_vec2 == 0:
            return 0.0
        return np.dot(vec1 / norm_vec1, vec2 / norm_vec2)

    def find_similar_face(self, current_face_vector):
        """
        Searches for a similar face vector in 
        Returns: (status, face_id, similarity_score)
                status can be "seen", error_searching
                Returns (None, None, None) if not sufficiently similar face is found.
        """
        if not self.redis:
            return "error_redis_unavailable", None, None
        if current_face_vector is None:
            return "error_no_vector", None, None

        best_match_id = None
        highest_similarity = -1.0  # Cosine similarity is in [-1, 1]

        try:
            # scan_iter can be slow in Redis with many keys.
            # this part is the primary candidate for upgrading to Redis Stack VSS.
            for key_bytes in self.redis.scan_iter(match=f"{self.prefix}*"):
                key = key_bytes.decode('utf-8')
                stored_data_bytes = self.redis.hget(key, "embedding")
                if stored_data_bytes:
                    stored_vector = pickle.loads(stored_data_bytes)
                    sim = self._cosine_similarity(current_face_vector, stored_vector)
                    if sim > highest_similarity:
                        highest_similarity = sim
                        best_match_id = key

            if highest_similarity >= self.similarity_threshold:
                # Refresh TTl and last_seen
                self._update_face_metadata(best_match_id)
                return "seen", best_match_id, float(highest_similarity)
        except redis.exceptions.RedisError as e:
            _logger.error(f"Redis error during similarity seatch: {e}", exc_info=True)
            return "error_searching_redis", None, None
        except Exception as e:
            _logger.error(f"Unexpected error during similarity search: {e}", exc_info=True)
            return "error_searching_unknown", None, None

        return None, None, None  # No sufficiently similar match found

    def add_new_face(self, face_vector):
        if not self.redis:
            return "error_redis_unavailable", None
        if face_vector is None:
            return "error_no_vector", None

        # Generate a unique id for the new face
        face_id = f"{self.prefix}{uuid.uuid4()}"
        vector_bytes = pickle.dumps(face_vector)  # Serialize the nu,py array (list of floats)
        current_time = int(time.time())

        try:
            # Store vector and metadata as a Redis Hash
            self.redis.hset(face_id, mapping={
                "embedding": vector_bytes,
                "model_name": self.embedding_model_name,
                "first_seen_timestamp": current_time,
                "last_seen_timestamp": current_time,
                "hit_count": 1  # how many times face or similar has been seen
            })
            self.redis.expire(face_id, self.ttl_seconds)  # Time to live for this entry
            _logger.info(f"New face vector stored as {face_id} with TTL {self.ttl_seconds}s")
            return "new", face_id  # Return new status and the generated ID
        except redis.exceptions.RedisError as e:
            _logger.error(f"Redis error storing new face {face_id}: {e}", exc_info=True)
            return "error_storing_redis", None
        except Exception as e:
            _logger.error(f"Unexpected error storing new face {face_id}: {e}", exc_info=True)
            return "error_storing_unknown", None

    def _update_face_metadata(self, face_id):
        """Updates metadata (last_seen, hit_count, TTL) for existing face vector"""
        if not self.redis:
            return
        try:
            current_time = int(time.time())
            # Update last seen timestamp and increment hit count using HINCRBY for atomic increment
            self.redis.hmset(face_id, {"last_seen_timestamp": current_time})
            self.redis.hincrby(face_id, "hit_count", 1)
            # refresh the ttl
            self.redis.expire(face_id, self.ttl_seconds)
        except redis.exceptions.RedisError as e:
            _logger.warning(f"Redis error updating metadata for {face_id}: {e}")


def initialize_global_services(app):
    global embedding_service, face_vector_store, mivolo_predictor, redis_connection

    app_logger = app.logger  # Flask app logger

    try:
        embedding_service = EmbeddingService(
            model_name=app.config['FACE_EMBEDDING_MODEL'],
            logger=app_logger
        )
    except RuntimeError as e:
        app_logger.error(f"CRITICAL: Failed to initialize embedding service: {e}. Face matching disabled.")
    except Exception as e:
        app_logger.error(f"CRITICAL: Unexpected error initializing embedding service: {e}. Face matching disabled.",
                         exc_info=True)
    
    # initialize Redis and face vector store
    if embedding_service:
        try:
            app_logger.info(f"Attempting Redis connection at {app.config['REDIS_HOST']}:{app.config['REDIS_PORT']}")
            redis_connection = redis.Redis(
                host=app.config['REDIS_HOST'],
                port=app.config['REDIS_PORT'],
                db=app.config['REDIS_DB'],
                socket_connect_timeout=5,
                health_check_interval=30,
                decode_responses=False  # store bytes
            )
            redis_connection.ping()  # Check if Redis is reachable
            app_logger.info("Redis connection established.")
            face_vector_store = RedisFaceVectorStore(
                redis_connection,
                app.config['FACE_TTL_SECONDS'],
                app.config['SIMILARITY_THRESHOLD'],
                app.config['FACE_EMBEDDING_MODEL'],
                app_logger
            )
        except redis.exceptions.ConnectionError as e:
            app_logger.error(f"Redis connection error: {e}. Face matching disabled.")
            redis_connection = None
            face_vector_store = None
        except Exception as e:
            app_logger.error(f"Unexpected error initializing Redis: {e}. Face matching disabled.", exc_info=True)
            face_vector_store = None
    else:
        app_logger.warning("Embedding service not initialized. Face matching disabled.")

    # Mivolo Predictor
    try:
        if app.config['DEVICE'] == "cuda":
            app_logger.info("Setting cuda options for MiVOLO predictor.")
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.benchmark = True
        
        mivolo_args = argparse.Namespace(
            input=None, output=app.config['OUTPUT_DIR'],
            detector_weights=app.config['MIVOLO_DETECTOR_WEIGHTS'],
            checkpoint=app.config['MIVOLO_MODEL_PATH'],
            with_persons=False, disable_faces=False,
            draw=app.config['DRAW_OUTPUT'], device=app.config['DEVICE']
        )
        mivolo_predictor = Predictor(mivolo_args)
        app_logger.info("MiVOLO predictor initialized successfully.")
        if app.config['USE_HALF_PRECISION'] and app.config['DEVICE'] == "cuda":
            mivolo_predictor.age_gender_model.model.half()
            _logger.info("Model converted to half precision. ")
    except Exception as e:
        _logger.error(f"Error loading predictor: {e}", exc_info=True)
        mivolo_predictor = None