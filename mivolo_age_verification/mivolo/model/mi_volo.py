import torch
import torch.nn as nn
import numpy as np
from typing import Dict, Optional, Tuple, List
from timm.data import resolve_data_config, IMAGENET_DEFAULT_MEAN, IMAGENET_DEFAULT_STD

import logging
from data.misc import prepare_classification_images, class_letterbox
from mivolo.model.create_timm_model import create_model
from mivolo.structures import PersonAndFaceCrops, PersonAndFaceResult

_logger = logging.getLogger("MiVOLO")
has_compile = hasattr(torch, "compile")


class Meta:
    def __init__(self):
        self.min_age = None
        self.max_age = None
        self.avg_age = None
        self.num_classes = 4

        self.in_chans = 3
        self.with_persons_model = False
        self.disable_faces = False
        self.use_persons = False
        self.only_age = False

        self.num_classes_gender = 2
        self.input_size = 224

    def load_from_ckpt(self, ckpt_path: str,
                       disable_faces: bool = False,
                       use_persons: bool = False,
                       device: str = "cuda") -> "Meta":
        _logger.info(f"Meta.load_from_ckpt: Initial use_person arg: {use_persons}")

        self.disable_faces = disable_faces
        # use provided device for loading the checkpoint
        map_location = device if torch.cuda.is_available else "cpu"
        state = torch.load(ckpt_path, map_location=map_location, weights_only=False)
        # Handle state dict access
        state_dict = state.get("state_dict", state)  # Try to get state dict, fallback to state itself

        # determine self.with_persons_model
        # force the model to be 3 channel, regardless or checkpoint content.
        if not use_persons:
            self.with_persons_model = False
            _logger.info(f"Meta forced with with_persons_model = False 3-ch because use_persons argument was False.")
        elif isinstance(state, dict) and state.get("with_persons_model") is not None:
            # checkpoint explicitly says
            self.with_persons_model = state["with_persons_model"]
            _logger.info(f"Meta: with_persons_model from checkpoint metadata: {self.with_persons_model}")
        elif state_dict is not None:
            # Heuristic based on PatchEmbed keys
            if state_dict.get("patch_embed.conv1.0.weight") is not None and \
                    state_dict.get("patch_embed.conv2.0.weight") is not None and \
                    state_dict.get("patch_embed.map.mlp.fc1.weight") is not None:
                self.with_persons_model = True
                _logger.info("Meta: Inferred with_person_model = True (6-ch) from patchEmbed keys in checkpoint.")
            elif state_dict.get("patch_embed.conv.0.weight") is not None:
                self.with_persons_model = False
                _logger.info("Meta: Inferred with_person_model = False (3-ch) from patchEmbed keys in checkpoint.")
            else:
                self.with_persons_model = False  # Default to 3-ch if unsure from keys
                _logger.warning("Meta: Could not infer with_persons_model from checkpoint keys. Defaulting to False(3-ch)")
        else:
            self.with_persons_model = False
            _logger.warning("Meta: No checkpoint or state_dict. Defaulting to False(3-ch)")

        # self.min_age = state["min_age", 0] if isinstance(state, dict) else 0
        # self.max_age = state["max_age", 100] if isinstance(state, dict) else 100
        # self.avg_age = state["avg_age", 50.0] if isinstance(state, dict) else False
        # self.only_age = state.get("no_gender", False) if isinstance(state, dict) else False
        # only_age = self.only_age

        # if "with_persons_model" in state:
        #     self.with_persons_model = state["with_persons_model"]
        # else:
        #     self.with_persons_model = True if state_dict.get("patch_embed.conv1.0.weight") in state else False

        # for classification models, we have 2 classes for age and 2 for gender
        # if only age is true, we have 2 outputs age classification only
        self.num_classes = 2 if self.only_age else 4
        # self.in_chans = 3 if not self.with_persons_model else 6
        self.in_chans = 3

        # we only care about face without bodies
        self.with_persons_model = False
        self.use_persons = use_persons and self.with_persons_model

        if state_dict and "pos_embed" in state_dict and state_dict["pos_embed"].ndim >= 2:
            # let's assume for mivolo_d1_384 it's 384
            num_pos_embed_tokens = state_dict["pos_embed"].shape[1]
            if num_pos_embed_tokens > 1000:
                pass

        if not self.with_persons_model and self.disable_faces:
            raise ValueError("You can not use disable-faces for face-only model")
        if self.with_persons_model and self.disable_faces and not self.use_persons:
            raise ValueError(
                "You can not disable faces and persons together."
                "Set --with-persons if you want to run with --disable-faces"
            )

        # Get input size from state_dict or pos_embed
        if "pos_embed" in state_dict:
            self.input_size = state_dict["pos_embed"].shape[1] * 16
        else:
            # Default standard input size if pos_embed not found
            self.input_size = 224

        return self

    def __str__(self):
        attrs = vars(self)
        attrs.update({"use_person_crops": self.use_person_crops, "use_face_crops": self.use_face_crops})
        return ", ".join("%s: %s" % item for item in attrs.items())

    @property
    def use_person_crops(self) -> bool:
        return self.with_persons_model and self.use_persons

    @property
    def use_face_crops(self) -> bool:
        return not self.disable_faces or not self.with_persons_model


class MiVOLO(nn.Module):
    def __init__(
        self,
        ckpt_path: str,
        device: str = "cuda",
        half: bool = True,
        disable_faces: bool = False,
        use_persons: bool = True,
        verbose: bool = True,
        drop_path_rate: float = 0.1,
        torchcompile: Optional[str] = None
    ):

        super(MiVOLO, self).__init__()

        self.verbose = verbose
        self.device = torch.device(device)
        self.half = half and self.device.type != "cpu"
        self.drop_path_rate = drop_path_rate

        self.meta: Meta = Meta().load_from_ckpt(ckpt_path, disable_faces, use_persons, device=device)
        # if self.verbose:
        #     _logger.info(f"Model meta:\n{str(self.meta)}")

        model_name = f"mivolo_d1_{self.meta.input_size}"
        self.model_name = model_name

        model_name = f"mivolo_d1_{self.meta.input_size}"
        self.model = create_model(
            model_name=self.model_name,
            num_classes=self.meta.num_classes,
            in_chans=self.meta.in_chans,
            pretrained=False,
            checkpoint_path=ckpt_path,
            filter_keys=["fds.", 'head.weight', 'head.bias', 'aux_head.weight', 'aux_head.bias'],
            drop_path_rate=self.drop_path_rate  # Explicitly set drop_path_rate to a float value
        )
        self.param_count = sum([m.numel() for m in self.model.parameters()])
        _logger.info(f"Model {model_name} created, param count: {self.param_count}")

        self.data_config = resolve_data_config(
            model=self.model,
            verbose=verbose,
            use_test_size=True
        )

        self.data_config["crop_pct"] = 1.0
        c, h, w = self.data_config["input_size"]
        assert h == w, "Incorrect data_config"
        self.input_size = w

        self.model = self.model.to(self.device)

        if torchcompile:
            assert has_compile, "A version of torch w/ torch.compile is required for --compile, possibly a nightly."
            torch._dynamo.reset()
            self.model = torch.compile(self.model, backend=torchcompile)

    def predict(
        self,
        image: np.ndarray,
        detected_bboxes: PersonAndFaceResult,
        stable_ages: Optional[Dict[int, float]] = None,
    ):
        """
        Args:
            collect_only: If true, only collect predictions without setting them in detected_bbox
        """
        if (
            (detected_bboxes.n_objects == 0)
            or (not self.meta.use_persons and detected_bboxes.n_faces == 0)
            or (self.meta.disable_faces and detected_bboxes.n_persons == 0)
        ):
            # nothing to process
            return None

        model_input, crop_types, det_indices = self.prepare_crops(image, detected_bboxes)

        if model_input is None:
            return  # nothing to process

        # Note: model_input already contains the properly concatenated crops based on model type
        output = self.inference(model_input)

        # Split det_indices into face and body indices based on crop types
        face_inds = [idx if typ == 0 else None for idx, typ in zip(det_indices, crop_types)]
        bodies_inds = [idx if typ == 1 else None for idx, typ in zip(det_indices, crop_types)]

        # write gender and age results into detected boxes
        self.fill_in_results(output, detected_bboxes, face_inds, bodies_inds, stable_ages)
        return None

    def fill_in_results(self, output, detected_bboxes, face_inds, bodies_inds, stable_ages: Optional[Dict[int, float]] = None):
        """
        Process model output and fill in age gender results into detected_bboxes

        For age classification:
        class 0: Child age <= 30
        class 1: Adult age > 30
        """
        AGE_THRESHOLD = 30

        if self.meta.only_age:
            age_output = output
            gender_probs, gender_indx = None, None
        else:
            # First 2 outputs are gender logits and last 2 are age classification logits
            gender_output = output[:, :2].softmax(-1)
            gender_probs, gender_indx = gender_output.topk(1)

            # Get age classification logits indices 2 and 3
            age_output = output[:, 2:4]  # slices the relevant part of the model's output for age

        assert output.shape[0] == len(face_inds) == len(bodies_inds)

        # log the shape of the output
        _logger.debug(f"Model output shape: {output.shape}, Age output shape: {age_output.shape}")

        # per face
        for index in range(output.shape[0]):
            face_ind = face_inds[index]
            body_ind = bodies_inds[index]

            # Retrieve the track Id from detection if available
            track_id = None
            if hasattr(detected_bboxes, 'yolo_results') and hasattr(detected_bboxes.yolo_results, 'boxes') and index < len(detected_bboxes.yolo_results.boxes):
                if detected_bboxes.yolo_results.boxes[index].id is not None:
                    track_id = int(detected_bboxes.yolo_results.boxes[index].id.item())

            # use stable age if provided for this track
            if stable_ages is not None and track_id in stable_ages:
                # If using stable ages, we already have the actual age
                raw_age = stable_ages[track_id]
                # convert to binary classification based on the thres
                age_class = 1.0 if raw_age > AGE_THRESHOLD else 0.0
                age = float(age_class)
                _logger.debug(f"""Using stable age {raw_age} for track {track_id} -> 
                              class {age_class} (threshold {AGE_THRESHOLD})""")
            else:
                # Get age logits/output
                raw_logit = age_output[index]
                _logger.debug(f"Raw age logits for detection {index}: {raw_logit.tolist()}")

                # # Check if we have a single value (regression) or multiple logits (classification)
                # if raw_logit.dim() == 0 or raw_logit.shape[0] == 1:
                #     # Single value case - treat as regression with sigmoid
                #     probability = torch.sigmoid(raw_logit).item()
                #     age_class = 1 if probability > 0.5 else 0
                #     age_confidence = probability if age_class == 1 else (1 - probability)
                #     age = float(age_class)
                # else:
                # Multiple logits case - treat as classification with softmax
                age_probs = torch.nn.functional.softmax(raw_logit, dim=0)  # Convert logits to probs
                age_class = torch.argmax(age_probs).item()  # predicted class index 0 or 1
                age_confidence = age_probs[age_class].item()  # probability of the predicted class
                age = float(age_class)  # Final class

                if face_ind is not None:
                    detected_bboxes.set_age_conf(face_ind, age_confidence)

                _logger.debug(f""""Age classification for detection {index}:
                                {'adult (>30)' if age == 1.0 else 'child (<=30)'}
                                (confidence: {age_confidence:.2f})""")

            detected_bboxes.set_age(face_ind, age)
            detected_bboxes.set_age(body_ind, age)

            # _logger.info(f"\tage: {age}")

            if gender_probs is not None:
                gender = "male" if gender_indx[index].item() == 0 else "female"
                gender_score = gender_probs[index].item()

                # _logger.info(f"\tgender: {gender} [{int(gender_score * 100)}%]")

                detected_bboxes.set_gender(face_ind, gender, gender_score)
                detected_bboxes.set_gender(body_ind, gender, gender_score)

    def prepare_crops(self,
                      image: np.ndarray,  # HWC uint8 numpy array
                      detected_bboxes: PersonAndFaceResult
                      ) -> Tuple[Optional[torch.Tensor], List[int], List[Optional[int]]]:  # Return type
        """
        Collects face and/or person crops, prepares them into normalized tensors,
        constructs the final model input tensor, and returns it along with
        mapping lists that align each crop in the model input back to its
        original detection index and type.

        Args:
            image: The og image HWC, BGR or RGB uint8 numppy array from which crops are taken.
            detected_bboxes: PersonAndFaceResult object containing detection info.

        Returns:
            Tuple[Optional[torch.Tensor], List[int], List[Optional[int]]]
                - model_input_crops (Tensor, Optional): The final batch of crops ready for the model.
                    Shape: (num_valid_crops, C,H,W). C is or 6. None if no valid crops.
                - final_crop_type_indices (List[int]): List of crop types 0: face, 1: person
                    corresponding to each row in model_input_crops.
                - final_crop_to_det_indices (List[Optional[int]]): List of original detection indices
                    corresponding to each row in model_input_crops
        """
        _logger.debug("prepare_crops: Starting crop preparation.")
        # _logger.debug(f"With persons: {self.meta.with_persons_model}")

        # Handle Image Format ensure HWC uint8 numpy
        # convert tensor to numpy if needed
        if isinstance(image, torch.Tensor):
            # Grayscale, convert !,H,W or RGB (3,H,W)
            # if image is in format (C, H, W) transpose to (H, W, C)
            if image.dim() == 3 and image.shape[0] in [1, 3]:
                if image.shape[0] == 1:  # convert RGB for consistency
                    image = image.repeat(3, 1, 1)
                image_np = image.permute(1, 2, 0).cpu().numpy()  # C,H,W -> H,W,C
            elif image.dim() == 4 and image.shape[0] == 1 and image.shape[1] in [1, 3]:  # B,C,H,W
                if image.shape[1] == 1:
                    image = image.repeat(3, 1, 1)
                image_np = image.squeeze(0).permute(1, 2, 0).cpu().numpy()  # B,C,H,W -> H,W,C
            else:
                _logger.error(
                    f"prepare_crops: Unexpected image tensor shape: {image.shape}. Expecting (C,H,W) or (1,C,H,W)")
                return None, [], []

            # Ensure it's [0, 1] float if coming from ToTensor, then scale to uint8
            # or if it was already unnormalized, ensure it's uint8
            # Assuming the input image to this function is always HWC uint8 numpy array
            # as established in model_manager.process_single_sample
            # If not, the conversion to uint8 from  normalized tensor should happen before calling this
            if image.is_floating_point():
                if image.max() <= 1.0 + 1e-5 and image.min() >= 0.0 - 1e-5:  # likely [0, 1] range
                    image_np = (image_np * 255.0).clip(0, 255).astype(np.uint8)
                else:
                    # Float but not [0, 1] could already normalized or other range
                    _logger.warning(f"""prepare_crops: Float tensor input not in [0, 1] range
                                    (min: {image.min()}, max: {image.max()}). Attempting direct astype(uint8).
                                    Data might be incorrect""")
                    image_np = image_np.astype(np.uint8)  # This might be problematic
            elif image_np.dtype != np.uint8:  # Not float but not uint8
                image_np = image_np.astype(np.uint8)  # Convert to uint8
            else:
                image_np = image.cpu().numpy().astype(np.uint8)  # Assume it's already scaled or handled appropriately
        elif isinstance(image, np.ndarray):
            if image.dtype != np.uint8:
                image_np = image.astype(np.uint8)  # Ensure uint8
            else:
                image_np = image
        else:
            _logger.error(f"prepare_crops: Invalid image type: {type(image)}. Expecting np.ndarray for torch.Tensor.")
            return None, [], []

        _logger.debug(f"prepare_crops: Image for cropping: shape: {image_np.shape}, dtype: {image_np.dtype}")

        # Associate faces with persons
        if self.meta.use_person_crops and self.meta.use_face_crops:
            detected_bboxes.associate_faces_with_persons()

        # Collect raw crop images (numpy arrays) and their original detection indices
        # collect_crops gets raw image regions based on bounding boxes.
        # get_faces_with_bodies alignes them: if a face has a body and vice versa,
        # they appear at the same index in their respective lists. If one is missing, it's None.
        raw_crops_obj: PersonAndFaceCrops = detected_bboxes.collect_crops(image_np)
        _logger.debug(f"""prepare_crops: Collected raw crops. Num face slots: {len(raw_crops_obj.crops_faces)}, 
                      Num person slots: {len(raw_crops_obj.crops_persons)}""")

        num_actual_face_crops = sum(1 for crop_dict in [raw_crops_obj.crops_faces,
                                                        raw_crops_obj.crops_faces_wo_body] for crop in crop_dict.values() if crop is not None)
        _logger.debug(f"prepare_crops: Collected raw crops. Actual valid face crops: {num_actual_face_crops}...")

        (original_body_inds, raw_body_crops), \
            (original_face_inds, raw_face_crops) = raw_crops_obj.get_faces_with_bodies(
            use_persons=self.meta.use_person_crops,  # wether to consider person crops
            use_faces=self.meta.use_face_crops  # whether to consider face crops
        )
        # original face indices and original body indices are lists of same length
        # Each element is an original detection index from detected_bboxes or None
        # raw face crops and raw body crops are lists of np.array images or None, aligned with indices.
        num_potential_slots = len(original_face_inds)  # should be same as len(original_body_indices)
        if self.verbose:
            _logger.debug(f"prepare_crops: Got {num_potential_slots} aligned slots from get_faces_with_bodies.")
            limit = min(5, num_potential_slots)
            _logger.debug(f"  First {limit}, original_face_indices: {original_face_inds[:limit]}")
            _logger.debug(f"  First {limit}, original_body_indices: {original_body_inds[:limit]}")

        # prepare normalized pytorch tensors for face and person crops separately.
        # prepare_classification_images handles None crops, resizing, ToTensor and normalization
        # It should return a possibly empty tenor: (num_valid_crops, 3, H, W)
        faces_input_tensor = prepare_classification_images(
            raw_face_crops, self.input_size, self.data_config["mean"], self.data_config["std"], device=self.device
        )

        person_input_tensor = prepare_classification_images(
            raw_body_crops, self.input_size, self.data_config["mean"], self.data_config["std"], device=self.device
        )

        if self.verbose:
            _logger.debug(
                f"prepare_crops: face_input_tensor shape: {faces_input_tensor.shape if faces_input_tensor is not None else 'None'}")
            _logger.debug(
                f"prepare_crops: person_input_tensor shape: {person_input_tensor.shape if person_input_tensor is not None else 'None'}")

        # Construct the final model_input_crops and Mapping Lists
        final_model_input_crops_list = []  # list to gather valid crop Tensors before stacking
        final_crop_type_indices = []  # Type for each crop in final_model_input_crops_list
        final_crop_to_det_indices = []  # Original detection index for each crop

        # Iterate through the Aligned slots from get faces with bodies
        # we need to keep track of which raw crops actually made it into face_input_tensor/person_input_tensor
        # This is tricky because prepare_classification_images filteres Nones.
        # A more robust wat: rebuild from processed tensors if possible, or process one-by-one

        # Let's process slot by slot to maintain clear mapping:
        processed_face_idx_counter = 0
        processed_person_idx_counter = 0

        for slot_idx in range(num_potential_slots):
            current_face_crop_tensor_slice = None
            current_person_crop_tensor_slice = None
            original_det_idx_for_this_slot = None
            crop_type_for_this_slot = -1  # undetermined

            # Check if a face crop was actually processed for this slot idx
            # original face indices[slot_idx] is the det_idx if face was present in this slot
            if raw_face_crops[slot_idx] is not None and self.meta.use_face_crops:
                if faces_input_tensor is not None and processed_face_idx_counter < faces_input_tensor.shape[0]:
                    # Keep as [1,C,H,W]
                    current_face_crop_tensor_slice = faces_input_tensor[processed_face_idx_counter:processed_face_idx_counter + 1]

                    original_det_idx_for_this_slot = original_face_inds[slot_idx]
                    crop_type_for_this_slot = 0  # Face type
                    processed_face_idx_counter += 1
                else:  # should not happen if logic is correct
                    _logger.warning(
                        f"prepare_crops: Mismatch for face crop at slot {slot_idx}. Raw crop existed but no tensor.")

            # check if a person crop was actually processed for this slot idx
            if raw_body_crops[slot_idx] is not None and self.meta.use_person_crops:
                if person_input_tensor is not None and processed_person_idx_counter < person_input_tensor.shape[0]:
                    current_person_crop_tensor_slice = person_input_tensor[processed_person_idx_counter:processed_person_idx_counter + 1]
                    if original_det_idx_for_this_slot is None:  # if face wasn't primary for this slot
                        original_det_idx_for_this_slot = original_body_inds[slot_idx]
                        crop_type_for_this_slot = 1  # person type if face wasn't chosen
                    elif crop_type_for_this_slot == 0 and original_body_inds[slot_idx] != original_det_idx_for_this_slot:
                        # this means face and body came from different original detections but were associated.
                        # the original det idx for this slot is already set to face's index
                        #  the crop type for this slot is already face (0) . This is usually fine.
                        pass
                    processed_person_idx_counter += 1
                else:
                    _logger.warning(
                        f"prepare_crops: Mismatch for person crop at slot {slot_idx}. Raw crop existed but no tensor.")

            # Now decide what to add to the final batch based on model type 3ch vs 6ch
            if self.meta.with_persons_model:  # Expects 6-channel input
                if current_face_crop_tensor_slice is not None and current_person_crop_tensor_slice is not None:
                    # Both are available, concatenate them
                    # (1, 6, H, W)
                    combined_crop = torch.cat((current_face_crop_tensor_slice, current_person_crop_tensor_slice), dim=1)
                    final_model_input_crops_list.append(combined_crop)
                    # For 6ch, the primary detection index is usually the face's if available
                    final_crop_to_det_indices.append(
                        original_face_inds[slot_idx] if original_face_inds[slot_idx] is not None else original_body_inds[slot_idx])
                    # prioritize face type if face is present
                    final_crop_type_indices.append(0 if original_face_inds[slot_idx] is not None else 1)
                    # else if one is missing, this slot is skipped for 6ch model that requires both
            else:  # Expects 3-ch in either face or person, typically face
                if self.meta.use_face_crops and current_face_crop_tensor_slice is not None:
                    final_model_input_crops_list.append(current_face_crop_tensor_slice)  # (1, 3, H, w)
                    final_crop_to_det_indices.append(original_face_inds[slot_idx])
                    final_crop_type_indices.append(0)  # Face
                elif self.meta.use_person_crops and not self.meta.use_face_crops and current_person_crop_tensor_slice is not None:
                    # only if faces are disabled and person crops are used
                    final_model_input_crops_list.append(current_person_crop_tensor_slice)  # 1, 3, H, W
                    final_crop_to_det_indices.append(original_body_inds[slot_idx])
                    final_crop_type_indices.append(1)  # person
                # else no suitable 3ch crop this slot

        # stack all collected valid crops into a single batch tensor
        if final_model_input_crops_list:
            model_input_crops = torch.cat(final_model_input_crops_list, dim=0)
            if self.verbose:
                _logger.debug(f"prepare_crops: Final model_input_crops created with shape: {model_input_crops.shape}")
                _logger.debug(
                    f"  final_crop_type_indices (len {len(final_crop_type_indices)}): {final_crop_type_indices[:5]}")
                _logger.debug(
                    f"  fina_crop_to_det_indices (len {len(final_crop_to_det_indices)}): {final_crop_to_det_indices[:5]}")
        else:  # No  valid crops were processed to form the model input
            model_input_crops = None
            # Or torch.empty for type consistency if downstream expects tensor
            # Ensure mapping lists are also empty
            final_crop_type_indices = []
            final_crop_to_det_indices = []
            if self.verbose:
                _logger.debug("prepare_crops: No valid model input crops were generated.")

        # The og method returned faces_input, person_input, faces_inds, bodies_inds
        # We now return the final model input and it's corresponding mapping lists.
        return model_input_crops, final_crop_type_indices, final_crop_to_det_indices

    def inference(self, model_input):
        """Run inference on the model"""
        with torch.no_grad():
            if self.half:
                model_input = model_input.half()
                # make sure model is also in half precision
                self.model = self.model.half()
            else:
                model_input = model_input.float()

            # forward pass through the model
            output = self.model(model_input)

            return output


if __name__ == "__main__":
    model = MiVOLO(
        "/home/<USER>/Documents/Anas/jmsc_age_detection/MiVolo_base_age_gender_Inference/age_verification/models/mivolo_base.pth.tar",
        half=True,
        device="cuda:0"
    )
