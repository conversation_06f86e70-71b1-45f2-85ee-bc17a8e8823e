import os
import logging
import tempfile
import uuid
import time
import pickle
import cv2
import numpy as np

from flask import Blueprint, request, jsonify, current_app, send_from_directory
from werkzeug.utils import secure_filename

from services import embedding_service, face_vector_store, mivolo_predictor, redis_connection

bp = Blueprint('transaction_api', __name__)


def get_transaction_logger(transaction_id_str):
    """ Gets or creates a logger for a specific transaction for the current request."""
    logger_name = f"txn_{transaction_id_str}"
    txn_logger = logging.getLogger(logger_name)

    # if handlers are already set e.g. for a very fast subsequent request in same worker
    # more importantly, if we want a fresh handler for each request to ensure file is open/writeable
    if not txn_logger.handlers or not isinstance(txn_logger.handlers[0], logging.FileHandler):
        txn_logger.handlers = []  # clear existing handlers
        log_file = os.path.join(current_app.config['LOGS_DIR'], f"{secure_filename(transaction_id_str)}.log")
        handler = logging.FileHandler(log_file)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - FrameID:%(frame_id)s - %(message)s')
        handler.setFormatter(formatter)
        txn_logger.addHandler(handler)
        txn_logger.addHandler(handler)
        txn_logger.setLevel(getattr(logging, current_app.config['LOG_LEVEL'], logging.INFO))
        txn_logger.propagate = False  # Don't send to app logger if it's also logging to console
    return txn_logger


def close_transaction_file_handlers(logger_instance):
    """closes the file handlers for a given logger instance. Critical after request"""
    if logger_instance:
        for handler in list(logger_instance.handlers):
            if isinstance(handler, logging.FileHandler):
                handler.close()
                logger_instance.removeHandler(handler)


def process_frame(full_frame_np, base_filename, app_logger, txn_logger=None):
    """
    Process a single image frame:
    1. Performs object detection faces, persons and age/gender via MiVOLO
    2. For detected faces, generates embeddings.
    3. Compares embeddings with those in Redis to identify new vs seen faces.
    4. Stores new face embeddings in Redis.
    5. Optionally saves an annotated version of the image.
    """

    output_image_url = None  # saved annotated image
    processed_detections_list = []  # hold info for each detected object
    annotated_image_for_saving_np = full_frame_np  # Defaults to original if mivolo fails

    if mivolo_predictor is None:
        app_logger.error("Mivolo predictor is not available")
        return False, {"detections": [], "error": "Mivolo model is unavailable."}
    
    try:
        detected_objects, annotated_image_for_saving_np = mivolo_predictor.recognize_image(full_frame_np.copy())

        frame_id_for_log = str(uuid.uuid4())[:8]

        # Iterate through each object detected by mivolo
        for i in range(detected_objects.n_objects):
            cls_id = int(detected_objects.yolo_results.boxes[i].cls.item())
            cls_name = detected_objects.yolo_results.names[cls_id]  # face or person
            bbox = detected_objects.yolo_results.boxes[i].xyxy[0].tolist()  # Bounding box
            # conf = detected_objects.yolo_results.boxes[i].conf.item()  # YOLO Confidence score

            if cls_name == 'face':
                age_class_value = None
                age_label_value = None
                age_conf = None

                face_processing_status = "not_processed_for_matching"
                identity_from_redis = None
                similarity_score_value = None

                # get age/gender from Mivolo
                if detected_objects.ages and i < len(detected_objects.ages) and detected_objects.ages[i] is not None:
                    age_class_value = int(detected_objects.ages[i])
                    age_label_value = "adult" if age_class_value == 1.0 else "child"
                    age_conf = float(detected_objects.age_conf[i])

                # Face embedding and Redis Matching
                # only perform if face and the req. services are available
                if embedding_service and face_vector_store:
                    try:
                        # crop the face original image using the bounding box
                        x1, y1, x2, y2 = map(int, bbox)
                        h_img, w_img = full_frame_np.shape[:2]  # og img dimensions
                        # Ensure crop coords are within bbox
                        face_crop_np = full_frame_np[max(0, y1):min(h_img, y2), max(0, x1):min(w_img, x2)]

                        # generate embedding for the current crop
                        current_face_vector = embedding_service.get_embedding(face_crop_np)

                        if current_face_vector:
                            # try to find a similar face in the vector store
                            status_match, matched_id, sim_score = face_vector_store.find_similar_face(current_face_vector)

                            if status_match == "seen":
                                face_processing_status = "seen"
                                identity_from_redis = matched_id
                                similarity_score_value = sim_score
                            # No similar face found, so it's new
                            elif status_match is None:
                                # add this new face to the vector store
                                face_processing_status, identity_from_redis = face_vector_store.add_new_face(
                                    current_face_vector)
                                # similarity score value remains none for new faces
                            else:
                                face_processing_status = status_match
                        # Failed to generate an embedding for this crop
                        else:
                            face_processing_status = "error_embedding_generation_failed"
                    except Exception as e:
                        app_logger.error(f"Error processing individual face (bbox {bbox}): {e}", exc_info=True)
                        face_processing_status = "error_embedding_generation_failed"
                elif cls_name == 'face':
                    # face but no embedding/redis available
                    face_processing_status = "face_matching_services_unavailable"

                # prepare dict for this detection
                detection_entry = {
                    "type": cls_name,
                    # "bbox": bbox,
                    # "yolo_conf": conf,
                    "age_class": age_class_value,
                    "age_label": age_label_value,
                    "age_conf": age_conf,
                    "face_status": face_processing_status,
                    "face_id": identity_from_redis,
                    "similarity": similarity_score_value
                }

                processed_detections_list.append(detection_entry)
                app_logger.info(f"""detction Entry: {detection_entry}""")

    except Exception as e:
        app_logger.error(f"Error in mivolo processing or face sub processing stage: {e}", exc_info=True)
        return False, {"detections": [], "error": f"Mivolo processing failed: {str(e)}"}
    
    if current_app.config['DRAW_OUTPUT'] and annotated_image_for_saving_np is not None and annotated_image_for_saving_np.size > 0:
        try:
            sanitized_filename = secure_filename(f"out_{base_filename}")
            # enusre filename ends with a common image extenstion
            _, ext = os.path.splitext(sanitized_filename)
            if not ext.lower() in [".jpg", ".jpeg", ".png", ".bmp"]:
                sanitized_filename += ".jpg"

            output_path_on_server = os.path.join(current_app.config['OUTPUT_DIR'], sanitized_filename)
            cv2.imwrite(output_path_on_server, annotated_image_for_saving_np)
            # Url for client to access image
            output_image_url = f"/{current_app.config['SERVED_OUTPUT_ALIAS']}/{sanitized_filename}"

            app_logger.info(f"Annotated image saved to {output_path_on_server}, accessible at {output_image_url}")
        except Exception as e:
            app_logger.error(f"Error saving output image: {e}", exc_info=True)

    # Prepare the final results dict for api response
    results_data = {"detections": processed_detections_list}
    if output_image_url:
        results_data["output_image_url"] = output_image_url
    elif current_app.config['DRAW_OUTPUT']:
        results_data["warning"] = "Annotated image processing or saving failed, URL not available."

    return output_image_url is not None, results_data


# ---- Transaction API Routes ----
@bp.route('/start_transaction', methods=["POST"])
def start_transaction():
    if not redis_connection:
        return jsonify({"error": "Redis unavailable for transaction management"}), 503

    data = request.get_json()
    if not data or "transaction_id" not in data:
        return jsonify({"error": "Missing transaction_id in request"}), 400
    
    new_txn_id = str(data["transaction_id"]).strip()
    if not new_txn_id:
        return jsonify({"error": "transaction id cannot be empty."}), 400
    
    # Check if another transaction is active using Redis
    active_txn_details_json = redis_connection.get(current_app.config['REDIS_TRANSACTION_KEY'])
    if active_txn_details_json:
        active_txn_details = pickle.loads(active_txn_details_json)
        if active_txn_details.get("id") == new_txn_id:
            return jsonify({"message": f"Transaction {new_txn_id} is already active."}), 200
        else:
            current_app.logger.warning(f"""Attempt to start new txn {new_txn_id}
                                       while {active_txn_details.get('id')} is active.""")
            return jsonify({"error":
                            f"Another transaction ({active_txn_details.get('id')}) is active. Stop it first."}), 409
    
    # start new transaction
    txn_details_to_store = {"id": new_txn_id, "start_time": time.time()}
    redis_connection.set(current_app.config['REDIS_TRANSACTION_KEY'], pickle.dumps(txn_details_to_store))

    # setup logger for this transaction for the current request context if needed, or just log start
    txn_logger = get_transaction_logger(new_txn_id)
    txn_logger.info("Transaction STARTED", extra={'frame_id': 'SYSTEM'})
    # close after this initial log to release file for other workers/requests
    close_transaction_file_handlers(txn_logger)

    current_app.logger.info(f"Transaction {new_txn_id} started.")
    return jsonify({"message": f"Transaction {new_txn_id} started successfully."}), 200


@bp.route('/process_frame', methods=["POST"])
def process_frame_route():
    if not redis_connection:
        return jsonify({"error": "Redis unavailable for transaction management"}), 503
    
    active_txn_details_json = redis_connection.get(current_app.config['REDIS_TRANSACTION_KEY'])
    if not active_txn_details_json:
        return jsonify({"error": "No active transaction. Call /start_transaction first."}), 403
    
    active_txn_details = pickle.loads(active_txn_details_json)
    current_transaction_id = active_txn_details.get('id')

    if "file" not in request.files:
        return jsonify({"error": "No file part in the request"}), 400
    file = request.files["file"]
    if file.filename == "":
        return jsonify({"error": "No file seletected"}), 400
    
    # get transaction logger for this request
    txn_logger = get_transaction_logger(current_transaction_id)
    temp_image_path = None
    try:
        base_filename = f"txn_{current_transaction_id}_{secure_filename(file.filename)}"
        fd, temp_image_path = tempfile.mkstemp(suffix=os.path.splitext(base_filename)[1],
                                               dir=current_app.config["TEMP_DIR"])
        os.close(fd)
        file.save(temp_image_path)

        img_np = cv2.imread(temp_image_path)
        if img_np is None:
            current_app.logger.error(f"Failed to read image {temp_image_path}")
            return jsonify({"error": "Failed to read image"}), 400
        
        _, results_package = process_frame(img_np, base_filename, current_app.logger, txn_logger)

        response_payload = {"message": f"Frame processed for transaction {current_transaction_id}"}
        response_payload.update(results_package)
        status_code = 500 if "error" in results_package else 200
        return jsonify(response_payload), status_code
    except Exception as e:
        current_app.logger.error(f"Error in /process_frame: {e}", exc_info=True)
        if txn_logger:
            txn_logger.error(f"Unhandled error in /process_frame: {e}", exc_info=True)
            return jsonify({"error": "Internal server error: {str(e)}"}), 500
    finally:
        if temp_image_path and os.path.exists(temp_image_path):
            try:
                os.remove(temp_image_path)
            except Exception as e:
                current_app.logger.error(f"Error removing temp file {temp_image_path}: {e}")


@bp.route("/stop_transaction", methods=["POST"])
def stop_transaction():
    if not redis_connection:
        return jsonify({"error": "Redis unavailable for transaction management"}), 503
    
    data = request.get_json()
    if not data or "transaction_id" not in data:
        return jsonify({"error": "Missing transaction_id in request"}), 400
    
    txn_id_to_stop = str(data["transaction_id"]).strip()
    
    # Check if another transaction is active using Redis
    active_txn_details_json = redis_connection.get(current_app.config['REDIS_TRANSACTION_KEY'])
    if not active_txn_details_json:
        return jsonify({"message": "No transaction is currently active to stop."}), 200
    
    active_txn_details = pickle.loads(active_txn_details_json)
    current_active_id = active_txn_details.get("id")

    if current_active_id != txn_id_to_stop:
        return jsonify({"error": f"""Attempt to stop wrong transaction. Active: {current_active_id},
                        Req: {txn_id_to_stop}"""}), 400

    # clear active transaction from Redis
    redis_connection.delete(current_app.config['REDIS_TRANSACTION_KEY'])

    txn_logger = get_transaction_logger(txn_id_to_stop)
    txn_logger.info("Transaction STOPPED", extra={'frame_id': 'SYSTEM'})
    # after the final log, close its handlers
    close_transaction_file_handlers(txn_logger)

    current_app.logger.info(f"Transaction {txn_id_to_stop} stopped.")
    return jsonify({"message": f"Transaction {txn_id_to_stop} stopped successfully."}), 200


# Route to serve static images
@bp.route('/<path:filename>')
def serve_output_image(filename):
    return send_from_directory(os.path.abspath(current_app.config['OUTPUT_DIR_FLASK']), filename)

    





