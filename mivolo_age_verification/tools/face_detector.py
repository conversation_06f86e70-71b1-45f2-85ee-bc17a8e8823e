import logging
import cv2
from pathlib import Path
from typing import List, Optional, Tuple
import torch
from ultralytics import YOLO
import atexit
import numpy as np


class FaceDetector:
    def __init__(self, min_crop_width: int = 32, min_crop_height: int = 32):
        self.min_crop_width = min_crop_width
        self.min_crop_height = min_crop_height
        self.model = None
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        self.face_class_id = None
        self.model_initialized = False
        self.input_size = (640, 640)  # Default YOLO input size
        
        # Register cleanup function
        atexit.register(self.cleanup)

    def cleanup(self):
        """Cleanup CUDA resources."""
        if self.model is not None:
            try:
                # Clear CUDA cache
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                # Delete model
                del self.model
                self.model = None
                self.model_initialized = False
            except Exception as e:
                logging.error(f"Error during cleanup: {e}")

    def initialize(self, model_path: Path, face_class_id: int = 1) -> bool:
        """Initialize the face detector with the specified model."""
        try:
            # Validate model path
            if not model_path.exists():
                logging.error(f"Model path does not exist: {model_path}")
                return False

            # Clear any existing CUDA cache
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            # Load model in inference mode only
            self.model = YOLO(str(model_path))
            
            # Set device and model to eval mode
            self.model.to(self.device)
            self.model.model.eval()
            
            # Disable training and set to inference mode
            self.model.train = False
            for param in self.model.model.parameters():
                param.requires_grad = False
            
            self.face_class_id = face_class_id
            
            # Warm up the model with a dummy input
            with torch.no_grad():
                dummy_input = torch.zeros((1, 3, *self.input_size), device=self.device)
                _ = self.model(dummy_input, verbose=False)
            
            self.model_initialized = True
            logging.info(f"Face detector initialized successfully on {self.device}")
            return True

        except Exception as e:
            logging.error(f"Failed to initialize face detector: {e}")
            self.cleanup()
            return False

    def preprocess_image(self, image_path: Path) -> Optional[Tuple[np.ndarray, Tuple[int, int], Tuple[int, int], float]]:
        """Preprocess image for face detection."""
        try:
            # Read image
            img = cv2.imread(str(image_path))
            if img is None:
                logging.error(f"Failed to read image: {image_path}")
                return None

            # Convert to RGB (YOLO expects RGB)
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Get original dimensions
            orig_h, orig_w = img.shape[:2]
            
            # Resize image to model input size while maintaining aspect ratio
            scale = min(self.input_size[0] / orig_w, self.input_size[1] / orig_h)
            new_w, new_h = int(orig_w * scale), int(orig_h * scale)
            
            # Resize image
            img = cv2.resize(img, (new_w, new_h))
            
            # Pad image to make it square
            padded_img = np.zeros((*self.input_size, 3), dtype=np.uint8)
            padded_img[:new_h, :new_w] = img
            
            return padded_img, (orig_w, orig_h), (new_w, new_h), scale

        except Exception as e:
            logging.error(f"Error preprocessing image {image_path}: {e}")
            return None

    def detect_faces(self, image_path: Path, conf_threshold: float = 0.3) -> List[List[int]]:
        """Detect faces in an image and return bounding boxes."""
        if not self.model_initialized:
            logging.error("Face detector not initialized")
            return []

        try:
            # Preprocess image
            preprocess_result = self.preprocess_image(image_path)
            if preprocess_result is None:
                return []
            
            padded_img, (orig_w, orig_h), (new_w, new_h), scale = preprocess_result

            # Run inference with torch.no_grad() for memory efficiency
            with torch.no_grad():
                results = self.model(padded_img, device=self.device, verbose=False)
            
            face_boxes = []
            if results and len(results) > 0:
                result = results[0]  # Get first result
                if hasattr(result, 'boxes') and result.boxes is not None:
                    boxes = result.boxes
                    for box in boxes:
                        # Check if this is a face detection (class_id matches)
                        if int(box.cls) == self.face_class_id and box.conf >= conf_threshold:
                            # Get box coordinates
                            x1, y1, x2, y2 = map(int, box.xyxy[0].cpu().numpy())
                            
                            # Scale coordinates back to original image size
                            x1 = int(x1 / scale)
                            y1 = int(y1 / scale)
                            x2 = int(x2 / scale)
                            y2 = int(y2 / scale)
                            
                            # Clamp coordinates to image boundaries
                            x1 = max(0, min(x1, orig_w))
                            y1 = max(0, min(y1, orig_h))
                            x2 = max(0, min(x2, orig_w))
                            y2 = max(0, min(y2, orig_h))
                            
                            # Validate box dimensions
                            width = x2 - x1
                            height = y2 - y1
                            if width >= self.min_crop_width and height >= self.min_crop_height:
                                face_boxes.append([x1, y1, x2, y2])
            
            # Clear CUDA cache after inference
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            return face_boxes
            
        except Exception as e:
            logging.error(f"Error during face detection for {image_path}: {e}")
            # Clear CUDA cache on error
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            return []

    def validate_bbox(self, bbox: List[int], image_size: Optional[Tuple[int, int]] = None) -> bool:
        """Validate if a bounding box meets minimum size requirements and is within image bounds."""
        if len(bbox) != 4:
            return False

        x0, y0, x1, y1 = bbox
        
        # Check if coordinates are valid
        if x0 >= 0 and y0 >= 0 and x1 > x0 and y1 > y0:
            # Check minimum size requirements
            width = x1 - x0
            height = y1 - y0
            if width < self.min_crop_width or height < self.min_crop_height:
                return False
            
            # Check if box is within image bounds if image size is provided
            if image_size is not None:
                img_w, img_h = image_size
                if x1 > img_w or y1 > img_h:
                    return False
            
            return True
        return False

    def get_model_info(self) -> dict:
        """Get information about the current model state."""
        return {
            "initialized": self.model_initialized,
            "device": self.device,
            "face_class_id": self.face_class_id,
            "input_size": self.input_size,
            "min_crop_size": (self.min_crop_width, self.min_crop_height)
        }
