import logging
import cv2
import albumentations as a
from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional


class DataAugmentor:
    def __init__(self, min_crop_width: int = 32, min_crop_height: int = 32):
        self.min_crop_width = min_crop_width
        self.min_crop_height = min_crop_height

    def get_aug_pipeline(self, level: int) -> a.<PERSON>:
        """Create augmentation pipeline based on level (1-5).
        Higher levels introduce more aggressive augmentations."""
        # Ensure level is between 1 and 5
        level = max(1, min(5, level))
        
        # Scale factor based on level (from 0.1 to 0.5) - reduced from 0.2-1.0
        scale = (level / 5.0) * 0.5

        # Basic augmentations (levels 1-5)
        transforms = [
            # RandAugment style color transforms with controlled magnitude
            a.OneOf([
                a.RandomBrightnessContrast(
                    brightness_limit=0.1 * scale,  # Reduced from 0.2
                    contrast_limit=0.1 * scale,    # Reduced from 0.2
                    p=0.5                         # Reduced from 0.7
                ),
                a.<PERSON>eSaturationValue(
                    hue_shift_limit=int(10 * scale),    # Reduced from 20
                    sat_shift_limit=int(15 * scale),    # Reduced from 30
                    val_shift_limit=int(10 * scale),    # Reduced from 20
                    p=0.5                              # Reduced from 0.7
                ),
                a.RGBShift(
                    r_shift_limit=int(10 * scale),      # Reduced from 20
                    g_shift_limit=int(10 * scale),      # Reduced from 20
                    b_shift_limit=int(10 * scale),      # Reduced from 20
                    p=0.5                              # Reduced from 0.7
                ),
                a.ChannelShuffle(p=0.2 * scale),        # Reduced from 0.3
                a.InvertImg(p=0.1 * scale if level >= 3 else 0),  # Reduced from 0.2
            ], p=0.6),  # Reduced from 0.8

            # Blur and Noise (more intense at higher levels)
            a.OneOf([
                a.GaussianBlur(blur_limit=(3, max(3, int(3 + 2 * scale) // 2 * 2 + 1))),  # Ensure odd kernel size
                a.MotionBlur(blur_limit=(5, max(5, int(3 + 2 * scale) // 2 * 2 + 1))),    # Ensure odd kernel size
                a.GaussNoise(p=0.4 * scale)  # Reduced from 0.6
            ], p=0.4 * scale + 0.1),  # Reduced from 0.5

            # Basic geometric transforms
            a.OneOf([
                a.HorizontalFlip(p=0.5),  # Reduced from 0.9
                a.VerticalFlip(p=0.5 * scale),  # Reduced from 0.1
                a.Rotate(
                    limit=int(15 * scale),  # Reduced from 15
                    p=0.7 * scale + 0.1,   # Reduced from 0.7
                    interpolation=cv2.INTER_LINEAR
                ),
            ], p=0.6 * scale + 0.1),  # Reduced from 0.7

            # Affine and Elastic Transforms
            a.OneOf([
                a.Affine(
                    scale=(1.0 - 0.05 * scale, 1.0 + 0.05 * scale),  # Reduced from 0.1
                    translate_percent=(-0.05 * scale, 0.05 * scale),  # Reduced from 0.1
                    rotate=(-int(8 * scale), int(8 * scale)),        # Reduced from 15
                    shear=(-int(3 * scale), int(3 * scale)),         # Reduced from 5
                    p=0.4 * scale + 0.1,                             # Reduced from 0.7
                    interpolation=cv2.INTER_LINEAR
                ),
                a.ElasticTransform(
                    alpha=int(50 * scale),   # Reduced from 100
                    sigma=max(1, int(5 * scale)),  # Reduced from 10
                    border_mode=cv2.BORDER_REFLECT_101,
                    p=0.3 * scale  # Reduced from 0.5
                ),
            ], p=0.3 * scale),  # Reduced from 0.5

            # Dropout transforms
            a.OneOf([
                a.CoarseDropout(
                    num_holes_range=(max(1, int(1 * scale)), max(2, int(4 * scale))),  # Reduced from 8
                    hole_height_range=(0.05 * scale, 0.15 * scale),  # Reduced from 0.1, 0.3
                    hole_width_range=(0.05 * scale, 0.15 * scale),   # Reduced from 0.1, 0.3
                    p=0.3 * scale  # Reduced from 0.5
                ),
                a.GridDropout(
                    unit_size_range=(max(8, int(16 * scale)), max(max(8, int(16 * scale)) + 1, int(24 * scale))),  # Reduced from 32
                    holes_number_xy=(max(1, int(2 * scale)), max(1, int(2 * scale))),  # Reduced from 4
                    p=0.3 * scale  # Reduced from 0.5
                ),
            ], p=0.2 * scale + 0.1),  # Reduced from 0.3
        ]

        # Add more aggressive augmentations for higher levels (4-5)
        if level >= 4:
            transforms.extend([
                # Heavy distortions
                a.OneOf([
                    a.OpticalDistortion(distort_limit=0.1 * scale, p=0.3 * scale),  # Reduced from 0.2, 0.5
                    a.GridDistortion(distort_limit=0.1 * scale, p=0.3 * scale),    # Reduced from 0.2, 0.5
                ], p=0.2 * scale),  # Reduced from 0.3

                # Lighting and color effects
                a.OneOf([
                    a.RandomFog(fog_coef_range=(0.05 * scale, 0.15 * scale), p=0.2 * scale),  # Reduced from 0.1-0.3, 0.3
                    a.RandomShadow(num_shadows_limit=(1, max(1, int(2 * scale))), p=0.2 * scale),  # Reduced from 3, 0.3
                    a.ColorJitter(
                        brightness=0.1 * scale,  # Reduced from 0.2
                        contrast=0.1 * scale,    # Reduced from 0.2
                        saturation=0.1 * scale,  # Reduced from 0.2
                        hue=0.05 * scale,        # Reduced from 0.1
                        p=0.2 * scale            # Reduced from 0.3
                    ),
                ], p=0.2 * scale),  # Reduced from 0.3

                # Advanced transforms
                a.OneOf([
                    a.Perspective(
                        scale=(0.03 * scale, 0.05 * scale),  # Reduced from 0.05-0.1
                        p=0.2 * scale,                       # Reduced from 0.3
                        interpolation=cv2.INTER_LINEAR,
                        border_mode=cv2.BORDER_REFLECT_101
                    ),
                ], p=0.2 * scale),  # Reduced from 0.3

                # More aggressive Dropout
                a.OneOf([
                    a.CoarseDropout(
                        num_holes_range=(max(1, int(1 * scale)), max(2, int(6 * scale))),  # Reduced from 2-12
                        hole_height_range=(0.1 * scale, 0.25 * scale),  # Reduced from 0.2-0.5
                        hole_width_range=(0.1 * scale, 0.25 * scale),   # Reduced from 0.2-0.5
                        p=0.2 * scale  # Reduced from 0.4
                    ),
                    a.GridDropout(
                        unit_size_range=(max(16, int(20 * scale)), max(max(16, int(20 * scale)) + 1, int(32 * scale))),  # Reduced from 24-48
                        holes_number_xy=(max(1, int(3 * scale)), max(1, int(3 * scale))),  # Reduced from 6
                        p=0.2 * scale  # Reduced from 0.4
                    ),
                ], p=0.1 * scale),  # Reduced from 0.2
            ])

        return a.Compose(transforms)

    def augment_sample(
        self,
        image_path: Path,
        boxes: Optional[List[List[int]]],  # Made boxes optional
        level: int,
        aug_base_dir: Path,
        original_relative_path: Path,
        stats: Dict[str, int]
    ) -> Tuple[Dict[str, Any], bool]:
        """Augment a single image at a specific level."""
        try:
            # Load and prepare image
            image = cv2.imread(str(image_path))
            if image is None:
                logging.warning(f"Could not read image for augmentation: {image_path}")
                # stats["aug_error"] += 1
                return None, False

            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # Get and apply augmentation pipeline
            pipeline = self.get_aug_pipeline(level)
            if pipeline is None:
                return None, False

            transformed = pipeline(image=image)

            # Save augmented image
            aug_filename = f"{original_relative_path.stem}_aug_{level}{original_relative_path.suffix}"
            aug_output_path = aug_base_dir / aug_filename
            aug_output_path.parent.mkdir(parents=True, exist_ok=True)

            save_image = cv2.cvtColor(transformed['image'], cv2.COLOR_RGB2BGR)
            if not cv2.imwrite(str(aug_output_path), save_image):
                raise IOError(f"Failed to write image to {aug_output_path}")

            return {
                "image_path": str(aug_output_path),
                "boxes": [],  # No boxes in output
                "num_faces": 1  # Assume one face per image since we're working with crops
            }, True

        except Exception as e:
            logging.error(f"Error during augmentation for {image_path.name}: {e}")
            # stats["aug_error"] += 1
            return None, False
