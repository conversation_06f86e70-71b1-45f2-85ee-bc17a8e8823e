import logging
import argparse
import random
import json
from collections import defaultdict
from tqdm import tqdm
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional
import cv2
import math

from face_detector import FaceDetector
from data_augmentor import DataAugmentor
from dataset_collector import DatasetCollector

MIN_CROP_WIDTH: int = 32
MIN_CROP_HEIGHT: int = 32

logging.basicConfig(level=logging.INFO)

image_extensions = {".jpg", ".png", ".jpeg"}


def parse_args():
    p = argparse.ArgumentParser(
        description="Prepare dataset annotations for AgeGenderDataset in JSON format."
    )
    p.add_argument("--seed", type=int, default=42)
    p.add_argument("--debug", action="store_true", default=False,
                   help="Enable debug logging for detailed information")

    p.add_argument("--dataset_dir", type=Path,
                   default="/home/<USER>/Documents/Anas/jmsc_age_detection/dataset/imdb_clean/",
                   help="Path to the original root dataset directory")
    p.add_argument("--output_dir", type=Path,
                   default="/home/<USER>/Documents/Anas/jmsc_age_detection/dataset/imdb_clean/",
                   help="Directory where the final annotations.json and derived data (crops, augs) will be saved.")

    p.add_argument("--save_face_crops", action="store_true", default=True,
                   help="If set, save face crops after initial detection/loading. Required for 'crop then augment' flow.")
    p.add_argument("--face_crops_dir", type=str, default="face_crops",
                   help="Subdirectory name for saving face crops relative to output_dir/split_name")

    p.add_argument("--split", type=float, default=0.8,
                   help="Train fraction, rest is val.")
    p.add_argument("--dataset_type", default="imdb_clean",
                   choices=["face_dataset", "utk_face", "knn_age", "lagenda", "imdb_clean"],
                   help="Type of the original dataset structure.")

    p.add_argument("--face_model_path", type=Path, default="models/yolov8x_person_face.pt")
    p.add_argument("--no_face_detection", action="store_true", default=True,
                   help="If set, skip running YOLO face detection. Relies on dataset providing boxes.")
    p.add_argument("--face_conf_thresh", type=float, default=0.3)
    p.add_argument("--face_class_id", type=int, default=1)  # Assuming class 1 is 'face' for yolov8x_person_face.pt

    # Augmentation arguments
    p.add_argument("--apply_aug", action="store_true", default=False)  # Keep default as False unless always augmenting
    p.add_argument("--aug_levels", type=int, default=3)
    p.add_argument("--aug_output_subdir", type=str, default="augmented_images",
                   help="Subdirectory under output_dir for augmented images.")

    # Class balancing arguments
    p.add_argument("--balance_classes", action="store_true", default=True,
                   help="Balance classes to have equal number of samples")
    p.add_argument("--samples_per_class_target", type=int, default=None,
                   help="Number of samples per class after balancing. If not specified, uses min class size.")
    p.add_argument("--allow_oversampling", action="store_true", default=True)

    p.add_argument("--face_padding", type=int, default=10,
                   help="Padding to apply around face bounding boxes during cropping.")

    p.add_argument("--max_samples", type=int, default=None,
                   help="Maximum number of samples to process. If not specified, all samples are used.")

    return p.parse_args()


def balance_dataset(raw_samples: List[Dict[str, Any]],
                    samples_per_class_target: Optional[int] = None,
                    age_threshold: int = 30,  # Example threshold
                    allow_oversampling: bool = False
                    ) -> List[Dict[str, Any]]:
    if not raw_samples:
        logging.warning("No raw_samples provided to balance_dataset")
        return []

    class_samples_for_balancing = defaultdict(list)
    for sample in raw_samples:
        age_label = sample.get('age')
        primary_age = -1
        if isinstance(age_label, list) and age_label:  # Should not happen if collector creates one sample per face
            primary_age = int(age_label[0])
        elif isinstance(age_label, (int, float)):
            primary_age = int(age_label)

        if primary_age < 0:
            logging.debug(f"Skipping sample with invalid age {age_label} during balancing.")
            continue
        balancing_label = 0 if primary_age < age_threshold else 1  # 0 for Child, 1 for Adult
        class_samples_for_balancing[balancing_label].append(sample)

    if not class_samples_for_balancing or len(class_samples_for_balancing) < 2:
        logging.warning("Not enough classes or no valid samples found for balancing. Returning original data.")
        all_valid_samples_for_balancing = [s for sl in class_samples_for_balancing.values() for s in sl]
        return all_valid_samples_for_balancing if all_valid_samples_for_balancing else raw_samples

    logging.info("\nClass distribution before balancing:")
    class_counts = {label: len(samples_list) for label, samples_list in class_samples_for_balancing.items()}
    for label, count in sorted(class_counts.items()):
        class_name = "Child" if label == 0 else "Adult"
        logging.info(f"Balancing class '{class_name}': {count} samples")

    # Dynamic balancing logic
    if samples_per_class_target is None:
        valid_class_lengths = [c for c in class_counts.values() if c > 0]
        if not valid_class_lengths:
            logging.warning("No samples in any balancing class after filtering invalid ages.")
            return [s for sl in class_samples_for_balancing.values() for s in sl]
        min_class_size = min(valid_class_lengths)
        samples_per_class_target = int(math.ceil(min_class_size * 1.5))
        logging.info(
            f"No specific target given, will oversample minority to {samples_per_class_target} and downsample majority to this value.")
    else:
        logging.info(f"Targeting {samples_per_class_target} samples per class.")

    balanced_raw_data = []
    for label, samples_list in tqdm(class_samples_for_balancing.items(), desc="Balancing raw samples"):
        current_class_size = len(samples_list)
        if current_class_size == 0:
            continue

        class_name = "Child" if label == 0 else "Adult"
        if current_class_size > samples_per_class_target:
            logging.info(f"Downsampling class '{class_name}' from {current_class_size} to {samples_per_class_target}")
            selected_samples = random.sample(samples_list, samples_per_class_target)
        elif current_class_size < samples_per_class_target and allow_oversampling:
            logging.info(f"Oversampling class '{class_name}' from {current_class_size} to {samples_per_class_target}")
            selected_samples = random.choices(samples_list, k=samples_per_class_target)
        else:
            if current_class_size < samples_per_class_target:
                logging.info(
                    f"Class '{class_name}' has {samples_per_class_target} samples (target {samples_per_class_target}), using all samples.")
            selected_samples = samples_list
        balanced_raw_data.extend(selected_samples)

    random.shuffle(balanced_raw_data)  # Shuffle after balancing

    logging.info(f"\nRaw Dataset balanced, {len(balanced_raw_data)} samples.")
    final_balanced_counts = defaultdict(int)
    for sample_data in balanced_raw_data:
        age_label = sample_data.get('age')
        primary_age = int(age_label[0]) if isinstance(age_label, list) and age_label else int(age_label)
        if primary_age >= 0:
            balancing_label = 0 if primary_age < age_threshold else 1
            final_balanced_counts[balancing_label] += 1
    logging.info("Final distribution of broad balancing classes:")
    for label, count in sorted(final_balanced_counts.items()):
        class_name = "Child" if label == 0 else "Adult"
        logging.info(f"Balancing Class '{class_name}': {count} samples")
    return balanced_raw_data


def save_crop_and_get_details(
    original_image_path_str: str,
    box_to_crop_on_image: List[int],  # Renamed: This box is already scaled and padded for the image
    age: int,
    gender: int,
    split: str,
    original_relative_to_dataset_dir: str,
    crop_idx: int,
    args: argparse.Namespace,  # args is kept for output_dir, face_crops_dir, etc.
    stats: Dict[str, int]
) -> Optional[Dict[str, Any]]:
    """Saves a single face crop using pre-calculated box coordinates and returns its metadata."""
    try:
        image = cv2.imread(original_image_path_str)
        if image is None:
            logging.warning(f"Could not read image for cropping: {original_image_path_str}")
            stats["read_error_cropping"] += 1
            return None

        # The incoming box_to_crop_on_image is already adjusted (scaled and padded by the caller).
        # No further padding or scaling should be done here.
        x1_final_crop, y1_final_crop, x2_final_crop, y2_final_crop = map(int, box_to_crop_on_image)

        # Clamp to actual image boundaries.
        h_img, w_img = image.shape[:2]
        x1_clamped = max(0, x1_final_crop)
        y1_clamped = max(0, y1_final_crop)
        x2_clamped = min(w_img, x2_final_crop)
        y2_clamped = min(h_img, y2_final_crop)

        if not (x1_clamped < x2_clamped and y1_clamped < y2_clamped):
            logging.warning(
                f"Invalid final crop dimensions for {original_image_path_str} with prepped box {box_to_crop_on_image} "
                f"(becomes [{x1_clamped},{y1_clamped},{x2_clamped},{y2_clamped}] on image {w_img}x{h_img}). Skipping crop."
            )
            stats["invalid_final_crop_dimensions"] += 1
            return None

        face_crop_img = image[y1_clamped:y2_clamped, x1_clamped:x2_clamped]
        if face_crop_img.size == 0:
            logging.warning(f"Empty crop for {original_image_path_str} with final box {box_to_crop_on_image} "
                            f"(clamped to [{x1_clamped},{y1_clamped},{x2_clamped},{y2_clamped}]). Skipping this crop.")
            stats["empty_final_crop"] += 1
            return None

        original_rel_path_obj = Path(original_relative_to_dataset_dir)
        crop_subfolder_path_under_split = original_rel_path_obj.parent
        crop_filename = f"{original_rel_path_obj.stem}_face_{crop_idx}{original_rel_path_obj.suffix}"

        # Full output path for the crop
        crop_output_dir_for_split = args.output_dir / args.face_crops_dir / split / crop_subfolder_path_under_split
        crop_output_dir_for_split.mkdir(parents=True, exist_ok=True)
        crop_abs_path = crop_output_dir_for_split / crop_filename

        if not cv2.imwrite(str(crop_abs_path), face_crop_img):
            logging.warning(f"Failed to write face crop: {crop_abs_path}")
            stats["crop_write_error"] += 1
            return None

        stats["crops_saved"] += 1

        # Path to the saved crop, relative to output_dir for the annotations file
        try:
            crop_relative_to_output_dir = str(crop_abs_path.relative_to(args.output_dir))
        except ValueError:
            crop_relative_to_output_dir = str(crop_abs_path)
            logging.warning(f"Could not make crop path {crop_abs_path} relative to output dir {args.output_dir}")

        return {
            "crop_image_path_relative_to_output": crop_relative_to_output_dir,
            "crop_image_path_absolute": str(crop_abs_path),
            "original_image_path_relative_to_dataset": original_relative_to_dataset_dir,
            "age": age,
            "gender": gender,
            "face_box_in_original": box_to_crop_on_image,  # Store the box that was intended for cropping (pre-clamping)
            "split": split,
        }
    except Exception as e:
        logging.error(
            f"Error during saving crop for {original_image_path_str}, box {box_to_crop_on_image}: {e}", exc_info=True)
        stats["crop_processing_error"] += 1
        return None


def process_and_crop_samples(
    samples_to_crop: List[Dict[str, Any]],  # These are from balanced_dataset
    face_detector: FaceDetector,
    args: argparse.Namespace,
    stats: Dict[str, int]  # Shared stats dict
) -> List[Dict[str, Any]]:
    """
    Processes samples to detect/validate face boxes and save face crops.
    Returns a list of dictionaries, each representing a successfully cropped face.
    """
    all_cropped_face_details = []

    stats.setdefault("detector_runs_as_fallback", 0)
    stats.setdefault("used_provided_bbox", 0)
    stats.setdefault("skipped_no_box_after_selection", 0)
    stats.setdefault("bbox_processing_error", 0)
    stats.setdefault("invalid_provided_bbox_format", 0)
    stats.setdefault("original_samples_processed_for_cropping", 0)
    stats.setdefault("invalid_final_crop_dimensions", 0)
    stats.setdefault("empty_final_crop", 0)

    logging.info(f"Starting face cropping process with {len(samples_to_crop)} samples")
    if args.debug:
        logging.info(f"Face detection enabled: {not args.no_face_detection}")
        logging.info(f"Face padding: {args.face_padding}")

    for sample_data in tqdm(samples_to_crop, desc="Stage 1: Cropping Faces"):
        original_image_abs_path_str = sample_data['image_path']
        age = sample_data.get('age')
        gender = sample_data.get('gender', -1)
        provided_bbox_raw = sample_data.get('bbox')  # This is now a list of bboxes
        current_split = sample_data.get("split", "train")

        if args.debug:
            logging.info(f"\nProcessing image: {original_image_abs_path_str}")
            logging.info(f"Age: {age}, Gender: {gender}")
            logging.info(f"Provided bbox: {provided_bbox_raw}")

        try:
            original_relative_to_dataset_dir = str(Path(original_image_abs_path_str)
                                                   .relative_to(args.dataset_dir))
        except ValueError:
            logging.warning(f"Could not make {original_image_abs_path_str} relative to "
                            f"{args.dataset_dir}. Using filename as fallback.")
            original_relative_to_dataset_dir = Path(original_image_abs_path_str).name

        if age is None or age == -1:
            if args.debug:
                logging.debug(f"Skipping {original_image_abs_path_str}: Invalid age ({age}).")
            stats["skipped_invalid_age_precrop"] += 1
            continue

        # Load image first to get dimensions for validation
        try:
            img_for_processing = cv2.imread(original_image_abs_path_str)
            if img_for_processing is None:
                logging.warning(f"Could not read image for cropping: {original_image_abs_path_str}")
                stats["read_error_cropping"] += 1
                continue
            img_h, img_w = img_for_processing.shape[:2]
            if args.debug:
                logging.info(f"Successfully loaded image: {img_w}x{img_h}")
        except Exception as e_read:
            logging.error(f"Error reading image for scaling/cropping: {e_read}")
            stats["read_error_cropping"] += 1
            continue

        face_boxes_for_cropping = []
        
        # Handle provided bounding boxes
        if provided_bbox_raw and isinstance(provided_bbox_raw, list):
            for bbox in provided_bbox_raw:
                if isinstance(bbox, (list, tuple)) and len(bbox) == 4:
                    try:
                        # Convert to integers
                        temp_bbox = [int(float(c)) for c in bbox]
                        
                        # Scale coordinates from 1024x1024 to actual image dimensions
                        scale_x = img_w / 1024.0
                        scale_y = img_h / 1024.0
                        
                        scaled_bbox = [
                            int(temp_bbox[0] * scale_x),
                            int(temp_bbox[1] * scale_y),
                            int(temp_bbox[2] * scale_x),
                            int(temp_bbox[3] * scale_y)
                        ]
                        
                        # Validate that the bbox is within image bounds
                        if (scaled_bbox[0] >= 0 and scaled_bbox[1] >= 0 and 
                            scaled_bbox[2] <= img_w and scaled_bbox[3] <= img_h and
                            scaled_bbox[0] < scaled_bbox[2] and scaled_bbox[1] < scaled_bbox[3]):
                            face_boxes_for_cropping.append(scaled_bbox)
                            if args.debug:
                                logging.info(f"Original bbox: {temp_bbox}")
                                logging.info(f"Scaled bbox: {scaled_bbox} for image {img_w}x{img_h}")
                            stats["used_provided_bbox"] += 1
                        else:
                            stats["invalid_provided_bbox_format"] += 1
                            if args.debug:
                                logging.info(f"Scaled bbox {scaled_bbox} outside image bounds {img_w}x{img_h}")
                    except (ValueError, TypeError) as e:
                        stats["invalid_provided_bbox_format"] += 1
                        if args.debug:
                            logging.info(f"Bbox has non-numeric or invalid coords: {bbox}, error: {e}")

        # If no valid provided boxes and face detection is enabled, try detection
        if not face_boxes_for_cropping and not args.no_face_detection:
            if args.debug:
                logging.info("No valid provided_bbox. Attempting face detection.")
            detected_boxes = face_detector.detect_faces(
                Path(original_image_abs_path_str),
                args.face_conf_thresh
            )
            if detected_boxes:
                face_boxes_for_cropping.extend(detected_boxes)
                stats["detected_bbox_used"] += len(detected_boxes)
                if args.debug:
                    logging.info(f"Detector found {len(detected_boxes)} boxes: {detected_boxes}")
            else:
                stats["detection_yielded_no_bbox"] += 1
                if args.debug:
                    logging.info("Detector yielded no boxes.")

        if not face_boxes_for_cropping:
            if args.no_face_detection:
                stats["skipped_no_bbox_no_detection"] += 1
                if args.debug:
                    logging.info("Skipping: No valid provided_bbox and detection disabled.")
            else:
                stats["skipped_no_box_for_crop"] += 1
                if args.debug:
                    logging.info("Skipping: No usable face box found after selection logic.")
            continue

        actual_crops_generated_for_this_image = 0
        for i, box_coords in enumerate(face_boxes_for_cropping):
            # Ensure coordinates are integers
            current_box = [int(c) for c in box_coords]

            # Apply padding directly, as box_coords are already scaled to image size
            padding_val = args.face_padding
            x1_padded = max(0, current_box[0] - padding_val)
            y1_padded = max(0, current_box[1] - padding_val)
            x2_padded = min(img_w, current_box[2] + padding_val)
            y2_padded = min(img_h, current_box[3] + padding_val)

            final_box_for_cropping_on_image = [x1_padded, y1_padded, x2_padded, y2_padded]

            if args.debug:
                logging.info(f"Processing box {current_box} -> padded to {final_box_for_cropping_on_image} for image {img_w}x{img_h}")

            crop_details = save_crop_and_get_details(
                original_image_abs_path_str,
                final_box_for_cropping_on_image,
                age, gender, current_split,
                original_relative_to_dataset_dir, i, args, stats
            )

            if crop_details:
                all_cropped_face_details.append(crop_details)
                actual_crops_generated_for_this_image += 1
                if args.debug:
                    logging.info(f"Successfully generated crop {i+1} for this image")
            else:
                if args.debug:
                    logging.info(f"Failed to generate crop {i+1} for this image")

        if actual_crops_generated_for_this_image > 0:
            stats["original_samples_processed_for_cropping"] += 1
            if args.debug:
                logging.info(f"Successfully processed image with {actual_crops_generated_for_this_image} crops")
        else:
            if args.debug:
                logging.info("No crops were generated for this image")

    if args.debug:
        logging.info("\nCropping statistics:")
        for key, value in stats.items():
            logging.info(f"{key}: {value}")

    return all_cropped_face_details


def main():
    args = parse_args()
    random.seed(args.seed)
    np.random.seed(args.seed)
    overall_stats = defaultdict(int)

    if not args.save_face_crops:
        logging.error("CRITICAL: --save_face_crops is FALSE. This script expects to save face crops to augment them.")
        logging.error("Please enable --save_face_crops or adapt the script if a different flow is intended.")
        return

    args.output_dir.mkdir(parents=True, exist_ok=True)

    face_detector = FaceDetector(MIN_CROP_WIDTH, MIN_CROP_HEIGHT)
    if not args.no_face_detection:
        if not face_detector.initialize(args.face_model_path, args.face_class_id):
            logging.error("Face detection initialization failed. Check model path and class ID.")
            return  # Exit if detector fails to initialize

    augmentor = DataAugmentor(MIN_CROP_WIDTH, MIN_CROP_HEIGHT)
    dataset_collector = DatasetCollector(args.dataset_dir)

    logging.info(f"Collecting samples for dataset type: {args.dataset_type}")
    all_samples_data = dataset_collector.collect_samples(args.dataset_type, max_samples=args.max_samples)

    if not all_samples_data:
        logging.error(f"No samples collected from {args.dataset_dir} for type {args.dataset_type}. Exiting.")
        return
    logging.info(f"Collected {len(all_samples_data)} raw samples with resolved paths.")

    initial_samples_count = len(all_samples_data)
    all_samples_data = [s for s in all_samples_data if s.get('path_resolved', True)]
    if len(all_samples_data) < initial_samples_count:
        msg = f"Filtered out {initial_samples_count - len(all_samples_data)} samples due to path resolution issues."
        logging.warning(msg)

    if not all_samples_data:
        logging.error("No samples remaining after path resolution checks. Exiting.")
        return

    samples_to_process_further = all_samples_data
    if args.balance_classes:
        logging.info("Balancing dataset...")
        samples_to_process_further = balance_dataset(
            all_samples_data,
            args.samples_per_class_target,
            allow_oversampling=args.allow_oversampling
        )
        logging.info(
            f"Dataset balanced, {len(samples_to_process_further)} samples remaining for cropping/augmentation.")
    else:
        logging.info("Skipping balancing.")

    if not samples_to_process_further:
        logging.error("No samples remaining after balancing. Exiting.")
        return

    rng = random.Random(args.seed)
    rng.shuffle(samples_to_process_further)

    # --- BEGIN MODIFICATION: Split data into train and val ---
    split_idx = int(len(samples_to_process_further) * args.split)
    train_samples_raw = samples_to_process_further[:split_idx]
    val_samples_raw = samples_to_process_further[split_idx:]

    logging.info(f"Splitting data: {len(train_samples_raw)} for train, {len(val_samples_raw)} for validation.")

    # Add 'split' information to each sample
    for sample in train_samples_raw:
        sample['split'] = 'train'
    for sample in val_samples_raw:
        sample['split'] = 'val'

    # Combine them back for processing, or process separately if preferred
    # For simplicity here, combining them as process_and_crop_samples uses sample['split']
    all_split_samples = train_samples_raw + val_samples_raw
    # Shuffle again if you want to mix train/val during the tqdm progress bar, though it's not strictly necessary
    # rng.shuffle(all_split_samples)
    # --- END MODIFICATION ---

    # Process and crop samples
    cropped_face_samples = process_and_crop_samples(all_split_samples, face_detector, args, overall_stats)

    if not cropped_face_samples:
        logging.error("No valid face crops were generated. Exiting.")
        return

    output_json_path = args.output_dir / "annotations.json"
    logging.info(f"Attempting to write annotations to {output_json_path}")

    # --- BEGIN MODIFICATION: Incremental JSON Writing ---
    try:
        args.output_dir.mkdir(parents=True, exist_ok=True)
        with open(output_json_path, "w") as f:
            f.write("[\n")  # Start JSON array
            first_entry = True

            # Iterate through cropped samples and write annotations immediately
            logging.info("Stage 2: Generating and Writing Annotations (including augmentations if enabled)")
            for crop_data in tqdm(cropped_face_samples, desc="Generating Annotations"):
                # Add comma between objects if not the first entry
                if not first_entry:
                    f.write(",\n")
                
                # Annotation for the original crop
                original_annotation = {
                    "image_path": crop_data["crop_image_path_relative_to_output"],
                    "ages": crop_data["age"],  # Single value, not a list
                    "genders": crop_data["gender"],  # Single value, not a list
                    "face_boxes": [],
                    "person_boxes": [],
                    "split": crop_data["split"],
                    "is_augmentation": False
                }

                json.dump(original_annotation, f, indent=2)
                first_entry = False
                overall_stats["original_crops_added_to_annotations"] += 1

                # Handle augmentations if applicable
                if args.apply_aug and crop_data["split"] == "train":
                    abs_crop_path = Path(crop_data["crop_image_path_absolute"])
                    base_name_for_aug = Path(crop_data["crop_image_path_relative_to_output"])
                    try:
                        path_parts = base_name_for_aug.parts
                        # Adjust base_name_for_aug to be relative to the split dir under face_crops_dir
                        # Example: face_crops/train/subdir/image.jpg -> subdir/image.jpg
                        if len(path_parts) > 2 and path_parts[0] == args.face_crops_dir:
                            # The part after args.face_crops_dir (index 0) and the split (index 1)
                            base_name_for_aug = Path(*path_parts[2:])
                        elif len(path_parts) > 1 and path_parts[0] == crop_data["split"]:
                            # If the path is already just split/subdir/image.jpg relative to output
                            base_name_for_aug = Path(*path_parts[1:])

                    except Exception as e_path:
                        logging.warning(f"Could not shorten path {base_name_for_aug} for aug naming: {e_path}")
                        base_name_for_aug = Path(crop_data["crop_image_path_relative_to_output"])  # Fallback to full path

                    for aug_level in range(1, args.aug_levels + 1):
                        aug_save_base_dir = args.output_dir / args.aug_output_subdir / crop_data["split"]

                        augmented_data, success = augmentor.augment_sample(
                            image_path=abs_crop_path,
                            boxes=None,  # Augmentations are applied to the full crop, not specific boxes within it
                            level=aug_level,
                            aug_base_dir=aug_save_base_dir,
                            original_relative_path=base_name_for_aug,
                            stats=overall_stats  # Pass stats dict
                        )

                        if success and augmented_data:
                            # Add comma between objects
                            f.write(",\n")
                            
                            # augmented_data["image_path"] returned by augment_sample is relative to aug_base_dir
                            # We need the path relative to output_dir
                            final_aug_path_relative_to_output = Path(
                                args.aug_output_subdir) / crop_data["split"] / Path(augmented_data["image_path"]).name  # Use .name to avoid recreating parent dirs in the path

                            aug_annotation = {
                                "image_path": str(final_aug_path_relative_to_output),
                                "ages": crop_data["age"],  # Single value, not a list
                                "genders": crop_data["gender"],  # Single value, not a list
                                "face_boxes": [],
                                "person_boxes": [],
                                "split": "train",  # Augmentations are only for the training split
                                "is_augmentation": True,
                                "aug_level": aug_level,
                                "original_crop_path": crop_data["crop_image_path_relative_to_output"]
                            }

                            json.dump(aug_annotation, f, indent=2)
                            overall_stats["augmentations_applied_successfully"] += 1
                        else:
                            overall_stats["augmentation_failures"] += 1

            f.write("\n]")  # End JSON array properly

        logging.info(f"Successfully saved annotations to {output_json_path}")

        if args.debug:
            logging.info("Overall Statistics:")
            for key, value in sorted(overall_stats.items()):
                logging.info(f"{key}: {value}")

    except Exception as e:
        logging.error(f"Failed to write annotations JSON file: {e}")
        if output_json_path.exists():
            logging.error(f"An error occurred during writing. The file {output_json_path} may be incomplete or invalid JSON.")
    # --- END MODIFICATION: Incremental JSON Writing ---


if __name__ == "__main__":
    main()