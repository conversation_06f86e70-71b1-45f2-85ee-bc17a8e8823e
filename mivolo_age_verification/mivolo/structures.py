from collections import defaultdict
import logging
import os
import math
import numpy as np
import cv2
import torch

from typing import Dict, List, Optional, Tuple
from copy import deepcopy
from ultralytics.engine.results import Results, Boxes
from ultralytics.utils.plotting import Annotator

from data.misc import aggregate_votes_winsorized, assign_faces, box_iou

os.unsetenv("CUBLAS_WORKSPACE_CONFIG")
_logger = logging.getLogger("__name__")
AGE_GENDER_TYPE = Tuple[Optional[float], Optional[str]]


class PersonAndFaceCrops:
    def __init__(self):
        # index of person analog result
        self.crops_persons: Dict[int, Optional[np.ndarray]] = {}
        # index of face analog results
        self.crops_faces: Dict[int, Optional[np.ndarray]] = {}
        # index of face along results
        self.crops_faces_wo_body: Dict[int, np.ndarray] = {}
        # # int: index of person along results
        self.crops_persons_wo_face: Dict[int, np.ndarray] = {}

    def _add_to_output(
        self, crops: Dict[int, np.ndarray], out_crops: List[np.ndarray], out_crop_inds: List[Optional[int]]
    ):
        inds_to_add = list(crops.keys())
        crops_to_add = list(crops.values())
        out_crops.extend(crops_to_add)
        out_crop_inds.extend(inds_to_add)

    def _get_all_faces(
        self, use_persons: bool, use_faces: bool
    ) -> Tuple[List[Optional[int]], List[Optional[np.ndarray]]]:
        """
        Returns
            if use_persons and use_faces
                faces: faces_with_bodies + faces_without_bodies + [None] * len(crops_persons_wo_faces)
            if use_persons and not use_faces
                faces: None * n_persons
            if not use_persons and use_faces:
                faces: faces_with_bodies + faces_without_bodies
        """

        def add_none_to_output(faces_inds, faces_crops, num):
            faces_inds.extend([None for _ in range(num)])
            faces_crops.extend([None for _ in range(num)])

        faces_inds: List[Optional[int]] = []
        faces_crops: List[Optional[np.ndarray]] = []

        if not use_faces:
            add_none_to_output(faces_inds, faces_crops, len(self.crops_persons) + len(self.crops_persons_wo_face))
            return faces_inds, faces_crops

        self._add_to_output(self.crops_faces, faces_crops, faces_inds)
        self._add_to_output(self.crops_faces_wo_body, faces_crops, faces_inds)

        if use_persons:
            add_none_to_output(faces_inds, faces_crops, len(self.crops_persons_wo_face))

        return faces_inds, faces_crops

    def _get_all_bodies(
        self, use_persons: bool, use_faces: bool
    ) -> Tuple[List[Optional[int]], List[Optional[np.ndarray]]]:
        """
        Returns
            if use_persons and use_faces
                persons: bodies_with_faces + [None] * len(faces_without_bodies) + bodies_without_faces
            if use_persons and not use_faces
                persons: bodies_with_faces + bodies_without_faces
            if not use_persons and use_faces
                persons: [None] * n_faces
        """

        def add_none_to_output(bodies_inds, bodies_crops, num):
            bodies_inds.extend([None for _ in range(num)])
            bodies_crops.extend([None for _ in range(num)])

        bodies_inds: List[Optional[int]] = []
        bodies_crops: List[Optional[np.ndarray]] = []

        if not use_persons:
            add_none_to_output(bodies_inds, bodies_crops, len(self.crops_faces) + len(self.crops_faces_wo_body))
            return bodies_inds, bodies_crops

        self._add_to_output(self.crops_persons, bodies_crops, bodies_inds)
        if use_faces:
            add_none_to_output(bodies_inds, bodies_crops, len(self.crops_faces_wo_body))

        self._add_to_output(self.crops_persons_wo_face, bodies_crops, bodies_inds)

        return bodies_inds, bodies_crops

    def get_faces_with_bodies(self, use_persons: bool, use_faces: bool):
        """
        Return
            faces: face_with_bodies, face_without_bodies, [None] * len(crops_persons_wo_face)
            persons: bodies_with_faces, [None] * len(faces_without_bodies), bodies_without_faces
        """

        bodies_inds, bodies_crops = self._get_all_bodies(use_persons, use_faces)
        faces_inds, faces_crops = self._get_all_faces(use_persons, use_faces)

        return (bodies_inds, bodies_crops), (faces_inds, faces_crops)

    def save(self, out_dir="output"):
        ind = 0
        os.makedirs(out_dir, exist_ok=True)
        for crops in [self.crops_persons, self.crops_faces, self.crops_faces_wo_body, self.crops_persons_wo_face]:
            for crop in crops.values():
                if crop is None:
                    continue
                out_name = os.path.join(out_dir, f"{ind}_crop.jpg")
                cv2.imwrite(out_name, crop)
                ind += 1


class PersonAndFaceResult:
    def __init__(self, results: Results):
        self.yolo_results = results

        # Ensure the names dictionary is properly set
        if not hasattr(results, 'names') or results.names is None or not isinstance(results.names, dict):
            # Set default names if missing or invalid
            results.names = {0: "face", 1: "person"}

        # Verify that the required class names are present
        names = set(results.names.values())
        assert "person" in names and "face" in names, "Missing required class names"

        n_objects = 0
        if hasattr(results, 'boxes') and results.boxes is not None and \
           hasattr(results.boxes, 'data') and results.boxes.data is not None and \
           isinstance(results.boxes.data, torch.Tensor) and results.boxes.data.ndim >= 1:
            # get count from the first dimension of the underlying data tensor
            n_objects = results.boxes.data.shape[0]

        self.ages: List[Optional[float]] = [None] * n_objects
        self.age_conf: List[Optional[float]] = [None] * n_objects
        self.genders: List[Optional[str]] = [None] * n_objects
        self.gender_scores: List[Optional[float]] = [None] * n_objects

        # initially no faces and persons are associated with each other
        self.face_to_person_map: Dict[int, Optional[int]] = {}
        self.unassigned_persons_inds: List[int] = []

        # populate map/lists after initialization, using safe count
        if n_objects > 0 and hasattr(self.yolo_results, 'names') and self.yolo_results.names is not None:
            try:
                # get bboxes_inds implicitly uses self.n_objects which we will fix next
                # for now let's assume get bboxes_inds works if n_objects is correct
                # Re-calculate indices here based on n_objects to initialize maps
                face_indices = self._get_bboxes_inds_interval("face", n_objects)  # uses internal helper for now
                person_indices = self._get_bboxes_inds_interval("person", n_objects)  # uses internal helper for now
                self.face_to_person_map = {ind: None for ind in face_indices}
                self.unassigned_persons_inds = person_indices
            except Exception as e:
                print(f"Error initializing maps in __init__: {e}", exc_info=True)

        # Batch predictions attributes
        self.buffer_size = 150  # 10 seconds * 30 fps
        self.frame_count = 0
        self.age_predictions: Dict[int, List[float]] = defaultdict(list)
        self.current_display_ages: Dict[int, float] = {}
        self.is_first_batch = True
        self.collecting = True

        if _logger.level <= logging.DEBUG:
            _logger.debug(f"Initialized PersonAndFaceResult with {n_objects} objects.")

    # Internal helper for __init__ to avoid dependency during init
    def _get_bboxes_inds_interval(self, category: str, n_obj: int) -> List[int]:
        """Internal helper to get indices, avoiding property use during init."""
        bboxes_inds: List[int] = []
        if n_obj == 0 or not hasattr(self.yolo_results, 'names') or self.yolo_results.names is None \
                or not hasattr(self.yolo_results, 'boxes') or self.yolo_results.boxes is None:
            return bboxes_inds

        for ind in range(n_obj):
            try:
                if hasattr(self.yolo_results.boxes[ind], 'cls') and self.yolo_results.boxes[ind].cls is not None:
                    cls_idx = int(self.yolo_results.boxes[ind].cls.item())
                    if cls_idx in self.yolo_results.names:
                        name = self.yolo_results.names[cls_idx]
                    if name.lower() == category.lower():
                        bboxes_inds.append(ind)
            except Exception as e:
                _logger.debug(f"Error getting bbox inds for category {category} at index {ind}: {e}")
                break
        return bboxes_inds

    # public method using property
    def get_bboxes_inds(self, category: str) -> List[int]:
        """Gets og detections indices (0 to n_obj -1) for specific category"""
        return self._get_bboxes_inds_interval(category, self.n_objects)

    def add_to_batch(self, raw_ages: bool = False) -> bool:
        """
        Add current frame's predictions to batch
        Args:
            raw_ages: If true, use raw age predictions instead of stabilzed ones
        Returns:
            bool: True if batch is full
        """
        # print("Add to batch called")
        # print(self.frame_count)
        # print(f"Buffer size: {self.buffer_size}")
        if not self.collecting:
            self.collecting = True
            print(f"starting new collection cycle")
            return False

        # collect predictions for each detected face
        for box_idx, det in enumerate(self.yolo_results.boxes):
            if det.id is not None and self.ages[box_idx] is not None:
                track_id = int(det.id.item())
                age = self.ages[box_idx]
                self.age_predictions[track_id].append(float(age))
                # _logger.debug
                print(
                    f"Added age {age} for track {track_id} (collected {len(self.age_predictions[track_id])}/{self.buffer_size})")

        self.frame_count += 1

        # check if already collected enough frames
        if self.frame_count >= self.buffer_size:
            # calculate mean age for each track
            new_mean_ages = {}
            for track_id, ages in self.age_predictions.items():
                if len(ages) >= 5:  # only use tracks with at least 5 predictions
                    mean_age = aggregate_votes_winsorized(np.array(ages))
                    new_mean_ages[track_id] = mean_age
                    print(f"Track {track_id}: Mean age calculated; {mean_age: .1f} from {len(ages)} samples")

            # update display ages
            if new_mean_ages:
                self.current_display_ages = new_mean_ages
                print(f"=== Batch complete: New display ages: {self.current_display_ages}")

            # reset for next batch
            self.frame_count = 0
            self.age_predictions.clear()
            self.is_first_batch = False
            return True

        return False

    def get_stable_age(self, track_id: int) -> Optional[float]:
        """Get stable age for a given track ID"""
        return self.current_display_ages.get(track_id)

    @property
    def n_objects(self) -> int:
        if self.yolo_results.boxes and self.yolo_results.boxes.data is not None:
            return self.yolo_results.boxes.data.shape[0]
        return 0

    @property
    def n_faces(self) -> int:
        return len(self.get_bboxes_inds("face"))

    @property
    def n_persons(self) -> int:
        return len(self.get_bboxes_inds("person"))

    @property
    def face_objects(self) -> Optional[Boxes]:
        """Return only the boxes corrsponding to faces."""
        inds = self.get_bboxes_inds("face")
        if not inds:
            return None
        # slicing a Boxes by a list of indices returns a new Boxes
        return self.yolo_results.boxes[inds]

    def get_distance_to_center(self, bbox_ind: int) -> float:
        """
        Calculate euclidian distance between bbox center and image center
        """
        im_h, im_w = self.yolo_results[bbox_ind].orig_shape
        x1, y1, x2, y2 = self.get_bbox_by_ind(bbox_ind).cpu().numpy()
        center_x, center_y = (x1 + x2) / 2, (y1 + y2) / 2
        dist = math.dist([center_x, center_y], [im_w / 2, im_h / 2])
        return dist

    def plot(
        self,
        conf=False,
        line_width=None,
        font_size=None,
        font="Arial.ttf",
        pil=False,
        img=None,
        labels=True,
        boxes=True,
        probs=True,
        ages=True,
        genders=True,
        gender_probs=False
    ):
        """
        Plots the detection results on an input RGB image. Accepts a numpy array cv2 for PIL image.
        Args:
        conf(bool): Wether to plot the detection confidence score
        line_width (float, optional): The line width of the bounding boxes. If None, scaled to Image size.
        probs: wether to plot classification probability
        ages(bool): wether to plot the age of the bounding boxes
        genders (bool): Wether to plot the genders of the bouding boxes

        Returns:
            (numpy.ndarray): A numpy array of the annotated image
        """

        # return self.yolo_results.plot()
        colors_by_ind = {}
        for face_ind, person_ind in self.face_to_person_map.items():
            if person_ind is not None:
                colors_by_ind[face_ind] = face_ind + 2
                colors_by_ind[person_ind] = face_ind + 2
            else:
                colors_by_ind[face_ind] = 0

        for person_ind in self.unassigned_persons_inds:
            colors_by_ind[person_ind] = 1

        names = self.yolo_results.names
        annotator = Annotator(
            deepcopy(self.yolo_results.orig_img if img is None else img),
            line_width,
            font_size,
            font,
            pil,
            example=names
        )
        pred_boxes = self.yolo_results.boxes

        if pred_boxes is None or self.n_objects == 0:
            return annotator.result()

        for bb_ind, d in enumerate(pred_boxes):
            # only process face detections
            if names[int(d.cls)] == "face":
                stable_age = None
                if d.id is not None:
                    track_id = int(d.id.item())
                    stable_age = self.get_stable_age(track_id)

                    # get current frame age
                    current_age = self.ages[bb_ind] if bb_ind < len(self.ages) else None

                    # Determine which age to display
                    display_age = stable_age if stable_age is not None else current_age

                    if display_age is not None:
                        print(f"Display age: {display_age}")

                        # convert binary classification to text
                        eligibility = "Eligible" if display_age == 1.0 else "Not Eligible"
                        # Draw box with eligibility status
                        label = eligibility
                        # use green for eligible red for not eligible
                        color = (0, 255, 0) if display_age == 1.0 else (0, 0, 255)
                        annotator.box_label(
                            d.xyxy.squeeze(),
                            label,
                            color=color
                        )
                else:
                    # Draw only box if no age
                    annotator.box_label(
                        d.xyxy.squeeze(),
                        None,
                        color=(0, 255, 0)
                    )

        return annotator.result()

    def set_tracked_age_gender(self, tracked_objects: Dict[int, List[AGE_GENDER_TYPE]]):
        """
        Update age and gender for objects based on history from tracked_objects.
        Args:
            tracked_objects (dict[int, list[AGE_GENDER_TYPE]]): info about tracked object by guid
        """

        for face_ind, person_ind in self.face_to_person_map.items():
            pguid = self._get_id_by_ind(person_ind)
            fguid = self._get_id_by_ind(face_ind)

            if fguid == -1 and pguid == -1:
                # YOLO might not assign assign ids for some objects in some cases:
                continue
            age, gender = self._gather_tracking_result(tracked_objects, fguid, pguid)
            if age is None or gender is None:
                continue
            self.set_age(face_ind, age)
            self.set_gender(face_ind, gender, 1.0)
            if pguid != -1:
                self.set_gender(person_ind, gender, 1.0)
                self.set_age(person_ind, age)

        for person_ind in self.unassigned_persons_inds:
            pid = self._get_id_by_ind(person_ind)
            if pid == -1:
                continue
            age, gender = self._gather_tracking_result(tracked_objects, -1, pid)
            if age is None or gender is None:
                continue
            self.set_gender(person_ind, gender, 1.0)
            self.set_age(person_ind, age)

    def _get_id_by_ind(self, ind: Optional[int] = None) -> int:
        if ind is None:
            return -1
        obj_id = self.yolo_results.boxes[ind].id
        if obj_id is None:
            return -1
        return obj_id.item()

    def get_bbox_by_ind(self, ind: int, im_h: int = None, im_w: int = None) -> Optional[torch.Tensor]:
        if ind < 0 or ind >= self.n_objects:
            _logger.warning(f"Attempted get bbox for invalid index {ind} (n_objects={self.n_objects})")
            return None
        try:
            box_object = self.yolo_results.boxes[ind]
            if hasattr(box_object, 'xyxy'):
                bb_t = box_object.xyxy.squeeze().float().clone()

                # ensure tensor is 1D x1, y1, x2, y2 before clamping
                if bb_t.ndim != 1 or bb_t.shape[0] != 4:
                    _logger.warning(f"Box object at index {ind} has invalid xyxy shape: {bb_t.shape}")
                    return None

                # clamp coordinates to provideed image dimensions operating on the clone
                if im_h is not None and im_w is not None:
                    # Ensure clamping dimensions are positive
                    im_h = max(0, int(im_h))
                    im_w = max(0, int(im_w))

                    bb_t[[1, 3]] = torch.clamp(bb_t[[1, 3]], 0, im_h - 1)
                    bb_t[[0, 2]] = torch.clamp(bb_t[[0, 2]], 0, im_w - 1)
                return bb_t
            else:
                _logger.warning(f"Box object at index {ind} does not have xyxy attribute")
                return None
        except Exception as e:
            _logger.error(f"Error getting bbox by ind {ind}: {e}")
            return None

    def set_age(self, ind: Optional[int], age: float):
        if ind is not None:
            self.ages[ind] = age

    def set_age_conf(self, ind: Optional[int], age_conf: float):
        if ind is not None and 0 <= ind < len(self.age_conf):
            self.age_conf[ind] = age_conf

    def set_gender(self, ind: Optional[int], gender: str, gender_score: float):
        if ind is not None:
            self.genders[ind] = gender
            self.gender_scores[ind] = gender_score

    @staticmethod
    def _gather_tracking_result(
        tracked_objects: Dict[int, List[AGE_GENDER_TYPE]],
        fguid: int = -1,
        pguid: int = -1,
        minimum_sample_size: int = 10
    ) -> AGE_GENDER_TYPE:
        assert fguid != -1 or pguid != -1, "Incorrect tracking behaviour"

        face_ages = [r[0] for r in tracked_objects[fguid] if r[0] is not None] if fguid in tracked_objects else []
        face_genders = [r[1] for r in tracked_objects[fguid] if r[1] is not None] if fguid in tracked_objects else []
        person_ages = [r[0] for r in tracked_objects[pguid] if r[0] is not None] if pguid in tracked_objects else []
        person_genders = [r[1] for r in tracked_objects[pguid] if r[1] is not None] if pguid in tracked_objects else []

        if not face_ages and not person_ages:  # both empty
            return None, None

        # play with different aggregation strategies
        # Face ages - predictions based on face or face + person, depends on history of object
        # person ages - predictions based on person or face + person, depends on history of object

        if len(person_ages + face_ages) >= minimum_sample_size:
            age = aggregate_votes_winsorized(person_ages + face_ages)
        else:
            face_age = np.mean(face_ages) if face_ages else None
            person_age = np.mean(person_ages) if person_ages else None
            if face_age is None:
                face_age = person_age
            if person_age is None:
                person_age = face_age
            age = (face_age + person_age) / 2.0

        genders = face_genders + person_genders
        assert len(genders) > 0
        # take mode of genders
        gender = max(set(genders), key=genders.count)

        return age, gender

    def get_results_for_tracking(self) -> Tuple[Dict[int, AGE_GENDER_TYPE], Dict[int, AGE_GENDER_TYPE]]:
        """
        Get objects from the current frame
        """
        persons: Dict[int, AGE_GENDER_TYPE] = {}
        faces: Dict[int, AGE_GENDER_TYPE] = {}

        names = self.yolo_results.names
        pred_boxes = self.yolo_results.boxes
        for _, (det, age, gender, _) in enumerate(zip(pred_boxes, self.ages, self.genders, self.gender_scores)):
            if det.id is None:
                continue
            cat_id, _, guid = int(det.cls), float(det.conf), int(det.id.item())
            name = names[cat_id]
            if name == "person":
                persons[guid] = (age, gender)
            elif name == "face":
                faces[guid] = (age, gender)

        return persons, faces

    def associate_faces_with_persons(self):
        face_bboxes_inds: List[int] = self.get_bboxes_inds("face")
        person_bboxes_inds: List[int] = self.get_bboxes_inds("person")

        face_bboxes: List[torch.tensor] = [self.get_bbox_by_ind(ind) for ind in face_bboxes_inds]
        person_bboxes: List[torch.tensor] = [self.get_bbox_by_ind(ind) for ind in person_bboxes_inds]

        self.face_to_person_map = {ind: None for ind in face_bboxes_inds}
        assigned_faces, unassigned_persons_inds = assign_faces(person_bboxes, face_bboxes)

        for face_ind, person_ind in enumerate(assigned_faces):
            face_ind = face_bboxes_inds[face_ind]
            person_ind = person_bboxes_inds[person_ind] if person_ind is not None else None
            self.face_to_person_map[face_ind] = person_ind

        self.unassigned_persons_inds = [person_bboxes_inds[person_ind] for person_ind in unassigned_persons_inds]

    def crop_object(
        self, full_image: np.ndarray, ind: int, cut_other_classes: Optional[List[str]] = None
    ) -> Optional[np.ndarray]:

        IOU_THRESH = 0.1
        MIN_PERSON_CROP_AFTERCUT_RATIO = 0.4
        CROP_ROUND_RATE = 0.3
        MIN_PERSON_SIZE = 50

        if isinstance(full_image, torch.Tensor):
            # if image is in format (C, H, W), Transpose to (H, W, C)
            if full_image.dim() == 3 and full_image.shape[0] == 3:
                full_image = full_image.permute(1, 2, 0)
            full_image = full_image.cpu().numpy()

        obj_bbox = self.get_bbox_by_ind(ind, *full_image.shape[:2])

        if obj_bbox is None:
            _logger.warning(f"crop_object: get_bbox_by_ind returned None for index {ind}. Cannot crop.")
            return None

        # turn each coordinates into python int
        # using round() before int() is generally safer for box coordinates
        x1, y1 = int(round(obj_bbox[0].item())), int(round(obj_bbox[1].item()))
        x2, y2 = int(round(obj_bbox[2].item())), int(round(obj_bbox[3].item()))
        _logger.debug(f"""crop_object: For det_idx {ind}, input img shape {full_image.shape},
                      scaled_bbox_float: {obj_bbox.tolist()}, int_coords: [{x1}, {y1}, {x2}, {y2}]""")
        cur_cat = self.yolo_results.names[int(self.yolo_results.boxes[ind].cls)]

        # slice validity before cropping
        # check if these integer coordinates form a valid slice
        # x1 < x2 and y1 < y2 and are within image bounds
        if not (0 <= y1 < full_image.shape[0] and \
                0 <= x1 < full_image.shape[1] and \
                y1 < y2 and x1 < x2 and \
                y2 <= full_image.shape[0] and \
                x2 <= full_image.shape[1]):
            _logger.warning(f"crop_object: Invalid slice coordinates for det_idx {ind}. "
                            f"x1={x1}, y1={y1}, x2={x2}, y2={y2} for image H={full_image.shape[0]}, W={full_image.shape[1]}. Returning None.")
            return None

        # get crop of face or person
        obj_image = full_image[y1:y2, x1:x2].copy()
        crop_h, crop_w = obj_image.shape[:2]

        # empty or too small crop
        if obj_image.size == 0 or crop_h < 2 or crop_w < 2:
            _logger.warning(f"crop_object: crop for det_idx {ind} is too small or empty. Shape: ({crop_h}, {crop_w})"
                            f"Original int_coords: [{x1},{y1},{x2},{y2}]. Returning None")
            return None

        if cur_cat == "person" and (crop_h < MIN_PERSON_SIZE or crop_w < MIN_PERSON_SIZE):
            return None

        if not cut_other_classes:
            return obj_image

        # calculate iou between obj_bbox and other bboxes
        other_bboxes: List[torch.tensor] = [
            self.get_bbox_by_ind(other_ind, *full_image.shape[:2]) for other_ind in range(len(self.yolo_results.boxes))
        ]

        iou_matrix = box_iou(torch.stack([obj_bbox]), torch.stack(other_bboxes)).cpu().numpy()[0]

        # cut out other objects in case of intersections
        for other_ind, (det, iou) in enumerate(zip(self.yolo_results.boxes, iou_matrix)):
            other_cat = self.yolo_results.names[int(det.cls)]
            if ind == other_ind or iou < IOU_THRESH or other_cat not in cut_other_classes:
                continue
            o_x1, o_y1, o_x2, o_y2 = det.xyxy.squeeze().type(torch.int32)

            # remap current_person_bbox to reference_person_bbox coordinates
            o_x1 = max(o_x1 - x1, 0)
            o_y1 = max(o_y1 - y1, 0)
            o_x2 = min(o_x2 - x1, crop_w)
            o_y2 = min(o_y2 - y1, crop_h)

            if other_cat != "face":
                if (o_y1 / crop_h) < CROP_ROUND_RATE:
                    o_y1 = 0
                if ((crop_h - o_y2) / crop_h) < CROP_ROUND_RATE:
                    o_y2 = crop_h
                if (o_x1 / crop_w) < CROP_ROUND_RATE:
                    o_x1 = 0
                if ((crop_w - o_x2) / crop_w) < CROP_ROUND_RATE:
                    o_x2 = crop_w

            obj_image[o_y1:o_y2, o_x1:o_x2] = 0

        remain_ratio = np.count_nonzero(obj_image) / (obj_image.shape[0] * obj_image.shape[1] * obj_image.shape[2])
        if remain_ratio < MIN_PERSON_CROP_AFTERCUT_RATIO:
            return None

        return obj_image

    def collect_crops(self, image) -> PersonAndFaceCrops:  # image is img for detector and logging 224x224
        crops_data = PersonAndFaceCrops()
        # This section iterates through the face_to_person map
        # so face_ind here is an og detection index that cooresponds to a face class
        for face_ind, person_ind in self.face_to_person_map.items():
            # face_ind is the og detection index for this face
            # pass the og index
            face_image = self.crop_object(image, face_ind, cut_other_classes=[])

            if person_ind is None:  # face detected but no associated person
                # store with og face ind as key
                crops_data.crops_faces_wo_body[face_ind] = face_image
            else:  # face detected and associated with a person
                # person ind is the og detection index for associated person
                person_image = self.crop_object(image, person_ind, cut_other_classes=["face", "person"])
                crops_data.crops_faces[face_ind] = face_image  # face ind is the key
                crops_data.crops_persons[person_ind] = person_image

        # iterates through unassigned persons
        for person_ind in self.unassigned_persons_inds:  # This are og detection indices of persons
            person_image = self.crop_object(image, person_ind, cut_other_classes=["face", "person"])
            crops_data.crops_persons_wo_face[person_ind] = person_image

        return crops_data
